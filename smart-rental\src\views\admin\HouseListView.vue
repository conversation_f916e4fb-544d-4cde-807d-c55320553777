<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import houseService from '@/services/house.service'
import type { HouseListItem } from '@/services/house.service'

const router = useRouter()

// 房源列表数据
const houses = ref<HouseListItem[]>([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  city: '',
  keyword: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部状态' },
  { value: '可租', label: '可租' },
  { value: '已租', label: '已租' },
  { value: '维护中', label: '维护中' },
  { value: '下架', label: '下架' }
]

// 城市选项
const cityOptions = [
  { value: '', label: '全部城市' },
  { value: '北京', label: '北京' },
  { value: '上海', label: '上海' },
  { value: '广州', label: '广州' },
  { value: '深圳', label: '深圳' },
  { value: '杭州', label: '杭州' },
  { value: '南京', label: '南京' }
]

// 获取房源列表
const fetchHouses = async () => {
  try {
    loading.value = true
    
    const params = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)
    
    const response = await houseService.getHouseList(params)
    houses.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch houses:', error)
    ElMessage.error('获取房源列表失败')
  } finally {
    loading.value = false
  }
}

// 审核房源
const approveHouse = async (houseId: number) => {
  try {
    await houseService.updateHouseStatus(houseId, '可租')
    
    // 更新状态
    const index = houses.value.findIndex(item => item.id === houseId)
    if (index !== -1) {
      houses.value[index].status = '可租'
    }
    
    ElMessage.success('房源审核通过')
  } catch (error) {
    console.error('Failed to approve house:', error)
    ElMessage.error('审核房源失败')
  }
}

// 下架房源
const rejectHouse = (houseId: number) => {
  ElMessageBox.prompt('请输入下架原因', '下架房源', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入下架原因'
  }).then(async ({ value }) => {
    try {
      await houseService.updateHouseStatus(houseId, '下架', value)
      
      // 更新状态
      const index = houses.value.findIndex(item => item.id === houseId)
      if (index !== -1) {
        houses.value[index].status = '下架'
      }
      
      ElMessage.success('房源已下架')
    } catch (error) {
      console.error('Failed to reject house:', error)
      ElMessage.error('下架房源失败')
    }
  }).catch(() => {})
}

// 删除房源
const deleteHouse = (houseId: number) => {
  ElMessageBox.confirm('确定要删除该房源吗？删除后无法恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await houseService.deleteHouse(houseId)
      
      // 从列表中移除
      houses.value = houses.value.filter(item => item.id !== houseId)
      total.value--
      
      ElMessage.success('房源已删除')
    } catch (error) {
      console.error('Failed to delete house:', error)
      ElMessage.error('删除房源失败')
    }
  }).catch(() => {})
}

// 查看房源详情
const viewHouseDetail = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 处理搜索
const handleSearch = () => {
  filters.page = 1
  fetchHouses()
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    city: '',
    keyword: '',
    page: 1,
    per_page: 10
  })
  
  fetchHouses()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchHouses()
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '可租': 'success',
    '已租': 'info',
    '维护中': 'warning',
    '下架': 'danger'
  }
  return statusMap[status] || 'info'
}

onMounted(() => {
  fetchHouses()
})
</script>

<template>
  <div class="house-list-container">
    <div class="page-header">
      <h1>房源管理</h1>
    </div>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="城市">
          <el-select v-model="filters.city" placeholder="选择城市" clearable>
            <el-option
              v-for="item in cityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索房源标题、地址"
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <div class="house-list" v-loading="loading">
      <el-empty v-if="houses.length === 0 && !loading" description="暂无房源记录" />
      
      <el-table :data="houses" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="房源信息" min-width="250">
          <template #default="{ row }">
            <div class="house-info">
              <el-image
                :src="row.main_image_url"
                :alt="row.title"
                class="house-image"
                fit="cover"
                @click="viewHouseDetail(row.id)"
              />
              <div>
                <div class="house-title" @click="viewHouseDetail(row.id)">
                  {{ row.title }}
                </div>
                <div class="house-address">{{ row.address }}</div>
                <div class="house-details">
                  {{ row.bedrooms }}室{{ row.bathrooms }}卫 · {{ row.area_sqm }}㎡
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="房东信息" width="150">
          <template #default="{ row }">
            <div>
              <div class="landlord-name">{{ row.landlord.full_name }}</div>
              <div class="landlord-phone">{{ row.landlord.phone_number }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="price_monthly" label="月租金" width="120">
          <template #default="{ row }">
            <span class="price">¥{{ row.price_monthly }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="city" label="城市" width="100" />
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="发布时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleString() }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewHouseDetail(row.id)"
            >
              查看详情
            </el-button>
            <el-button
              type="success"
              size="small"
              v-if="row.status === '待审核'"
              @click="approveHouse(row.id)"
            >
              审核通过
            </el-button>
            <el-button
              type="warning"
              size="small"
              v-if="row.status !== '下架'"
              @click="rejectHouse(row.id)"
            >
              下架
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteHouse(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.house-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.house-list {
  min-height: 400px;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-image {
  width: 80px;
  height: 60px;
  margin-right: 15px;
  border-radius: 4px;
  cursor: pointer;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
  cursor: pointer;
  color: #303133;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.house-details {
  font-size: 12px;
  color: #606266;
}

.landlord-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.landlord-phone {
  font-size: 12px;
  color: #909399;
}

.price {
  color: #f56c6c;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
