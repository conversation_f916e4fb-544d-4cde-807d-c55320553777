<script setup lang="ts">
import { ref } from 'vue'
import api from '@/services/api'
import authService from '@/services/auth.service'

const testResults = ref<any[]>([])
const loading = ref(false)

// 测试API连接
const testApiConnection = async () => {
  loading.value = true
  const result = {
    test: 'API连接测试',
    timestamp: new Date().toLocaleString(),
    success: false,
    data: null,
    error: null
  }

  try {
    console.log('测试API连接...')
    const response = await api.get('/houses')
    result.success = true
    result.data = response.data
    console.log('API连接成功:', response.data)
  } catch (error: any) {
    result.success = false
    result.error = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL
      }
    }
    console.error('API连接失败:', error)
  }

  testResults.value.unshift(result)
  loading.value = false
}

// 测试登录接口
const testLogin = async () => {
  loading.value = true
  const result = {
    test: '登录接口测试',
    timestamp: new Date().toLocaleString(),
    success: false,
    data: null,
    error: null,
    requestInfo: null as any
  }

  try {
    console.log('测试登录接口...')

    const loginData = {
      username: 'test',
      password: 'test123'
    }

    // 记录请求信息
    result.requestInfo = {
      url: `${import.meta.env.VITE_API_BASE_URL}/auth/login`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: loginData
    }

    console.log('发送登录请求:', result.requestInfo)

    const response = await api.post('/auth/login', loginData)
    result.success = true
    result.data = response.data
    console.log('登录接口响应:', response.data)
  } catch (error: any) {
    result.success = false
    result.error = {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        baseURL: error.config?.baseURL,
        headers: error.config?.headers
      }
    }
    console.error('登录接口失败:', error)
  }

  testResults.value.unshift(result)
  loading.value = false
}

// 测试原生fetch登录
const testFetchLogin = async () => {
  loading.value = true
  const result = {
    test: '原生Fetch登录测试',
    timestamp: new Date().toLocaleString(),
    success: false,
    data: null,
    error: null,
    requestInfo: null as any
  }

  try {
    console.log('测试原生fetch登录...')

    const loginData = {
      username: 'test',
      password: 'test123'
    }

    const url = `${import.meta.env.VITE_API_BASE_URL}/auth/login`

    // 记录请求信息
    result.requestInfo = {
      url: url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: loginData
    }

    console.log('发送原生fetch请求:', result.requestInfo)

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData)
    })

    const responseData = await response.json()

    if (response.ok) {
      result.success = true
      result.data = responseData
      console.log('原生fetch登录成功:', responseData)
    } else {
      result.success = false
      result.error = {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      }
      console.error('原生fetch登录失败:', responseData)
    }
  } catch (error: any) {
    result.success = false
    result.error = {
      message: error.message,
      type: 'NetworkError'
    }
    console.error('原生fetch网络错误:', error)
  }

  testResults.value.unshift(result)
  loading.value = false
}

// 测试环境变量
const testEnvironment = () => {
  const result = {
    test: '环境变量检查',
    timestamp: new Date().toLocaleString(),
    success: true,
    data: {
      VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
      VITE_APP_TITLE: import.meta.env.VITE_APP_TITLE,
      VITE_APP_ENV: import.meta.env.VITE_APP_ENV,
      MODE: import.meta.env.MODE,
      DEV: import.meta.env.DEV,
      PROD: import.meta.env.PROD
    },
    error: null
  }

  testResults.value.unshift(result)
}

// 清空测试结果
const clearResults = () => {
  testResults.value = []
}
</script>

<template>
  <div class="debug-container">
    <div class="debug-header">
      <h1>🔧 调试工具</h1>
      <p>用于测试前后端连接和API接口</p>
    </div>

    <!-- 测试按钮 -->
    <el-card class="test-buttons">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-button type="primary" @click="testEnvironment" block>
            检查环境变量
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="success" @click="testApiConnection" :loading="loading" block>
            测试API连接
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="warning" @click="testLogin" :loading="loading" block>
            Axios登录测试
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="info" @click="testFetchLogin" :loading="loading" block>
            Fetch登录测试
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="danger" @click="clearResults" block>
            清空结果
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 测试结果 -->
    <div class="test-results">
      <el-card v-for="(result, index) in testResults" :key="index" class="result-card">
        <template #header>
          <div class="result-header">
            <span class="test-name">{{ result.test }}</span>
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.success ? '成功' : '失败' }}
            </el-tag>
          </div>
          <div class="test-time">{{ result.timestamp }}</div>
        </template>

        <div v-if="result.requestInfo" class="result-content">
          <h4>请求信息:</h4>
          <pre class="json-display">{{ JSON.stringify(result.requestInfo, null, 2) }}</pre>
        </div>

        <div v-if="result.success && result.data" class="result-content">
          <h4>响应数据:</h4>
          <pre class="json-display">{{ JSON.stringify(result.data, null, 2) }}</pre>
        </div>

        <div v-if="!result.success && result.error" class="result-content">
          <h4>错误信息:</h4>
          <div class="error-info">
            <p><strong>错误消息:</strong> {{ result.error.message }}</p>
            <p v-if="result.error.status"><strong>状态码:</strong> {{ result.error.status }}</p>
            <p v-if="result.error.statusText"><strong>状态文本:</strong> {{ result.error.statusText }}</p>
            <p v-if="result.error.config"><strong>请求URL:</strong> {{ result.error.config.baseURL }}{{ result.error.config.url }}</p>
            <p v-if="result.error.config"><strong>请求方法:</strong> {{ result.error.config.method?.toUpperCase() }}</p>
          </div>

          <div v-if="result.error.data" class="error-data">
            <h5>服务器响应:</h5>
            <pre class="json-display">{{ JSON.stringify(result.error.data, null, 2) }}</pre>
          </div>
        </div>
      </el-card>

      <el-empty v-if="testResults.length === 0" description="暂无测试结果" />
    </div>
  </div>
</template>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-header {
  margin-bottom: 30px;
  text-align: center;
}

.debug-header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  color: #303133;
}

.debug-header p {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

.test-buttons {
  margin-bottom: 30px;
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-card {
  border-left: 4px solid #409EFF;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-name {
  font-weight: bold;
  font-size: 16px;
}

.test-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.result-content {
  margin-top: 15px;
}

.result-content h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.result-content h5 {
  margin: 15px 0 10px 0;
  color: #303133;
}

.json-display {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.error-info p {
  margin: 5px 0;
  font-size: 14px;
}

.error-data {
  margin-top: 15px;
}

@media (max-width: 768px) {
  .debug-container {
    padding: 15px;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
