<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import userService from '@/services/user.service'

const authStore = useAuthStore()

// 表单引用
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 用户信息
const userProfile = ref({
  id: 0,
  username: '',
  email: '',
  role: '',
  full_name: '',
  phone_number: '',
  avatar_url: '',
  registration_date: '',
  last_login_at: ''
})

// 个人资料表单
const profileForm = reactive({
  full_name: '',
  phone_number: '',
  email: ''
})

// 修改密码表单
const passwordForm = reactive({
  old_password: '',
  new_password: '',
  confirm_password: ''
})

// 表单验证规则
const profileRules = reactive<FormRules>({
  full_name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  phone_number: [
    { required: true, message: '请输入电话号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
})

// 密码验证规则
const validatePass = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入新密码'))
  } else {
    if (passwordForm.confirm_password !== '') {
      if (passwordFormRef.value) {
        passwordFormRef.value.validateField('confirm_password', () => null)
      }
    }
    callback()
  }
}

const validatePass2 = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入新密码'))
  } else if (value !== passwordForm.new_password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = reactive<FormRules>({
  old_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validatePass2, trigger: 'blur' }
  ]
})

// 加载状态
const profileLoading = ref(false)
const passwordLoading = ref(false)
const avatarLoading = ref(false)

// 获取用户信息
const fetchUserProfile = async () => {
  try {
    profileLoading.value = true
    
    // 模拟API调用
    setTimeout(() => {
      // 模拟数据
      userProfile.value = {
        id: authStore.user?.id || 0,
        username: authStore.user?.username || '',
        email: '<EMAIL>',
        role: authStore.user?.role || 'tenant',
        full_name: '张三',
        phone_number: '13800138000',
        avatar_url: 'https://via.placeholder.com/150',
        registration_date: '2023-01-15',
        last_login_at: '2023-05-20T10:30:00'
      }
      
      // 填充表单
      profileForm.full_name = userProfile.value.full_name
      profileForm.phone_number = userProfile.value.phone_number
      profileForm.email = userProfile.value.email
      
      profileLoading.value = false
    }, 500)
    
    // 实际API调用应该类似于:
    // const response = await userService.getCurrentUserProfile()
    // userProfile.value = response
    // profileForm.full_name = response.full_name
    // profileForm.phone_number = response.phone_number
    // profileForm.email = response.email
  } catch (error) {
    console.error('Failed to fetch user profile:', error)
    ElMessage.error('获取用户信息失败')
    profileLoading.value = false
  }
}

// 更新个人资料
const updateProfile = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        profileLoading.value = true
        
        // 模拟API调用
        setTimeout(() => {
          // 更新本地数据
          userProfile.value.full_name = profileForm.full_name
          userProfile.value.phone_number = profileForm.phone_number
          userProfile.value.email = profileForm.email
          
          ElMessage.success('个人资料更新成功')
          profileLoading.value = false
        }, 500)
        
        // 实际API调用应该类似于:
        // await userService.updateUserProfile({
        //   full_name: profileForm.full_name,
        //   phone_number: profileForm.phone_number,
        //   email: profileForm.email
        // })
      } catch (error) {
        console.error('Failed to update profile:', error)
        ElMessage.error('更新个人资料失败')
        profileLoading.value = false
      }
    }
  })
}

// 修改密码
const updatePassword = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        passwordLoading.value = true
        
        // 模拟API调用
        setTimeout(() => {
          // 清空表单
          passwordForm.old_password = ''
          passwordForm.new_password = ''
          passwordForm.confirm_password = ''
          
          ElMessage.success('密码修改成功')
          passwordLoading.value = false
        }, 500)
        
        // 实际API调用应该类似于:
        // await api.put('/users/me/password', {
        //   old_password: passwordForm.old_password,
        //   new_password: passwordForm.new_password
        // })
      } catch (error) {
        console.error('Failed to update password:', error)
        ElMessage.error('修改密码失败')
        passwordLoading.value = false
      }
    }
  })
}

// 上传头像
const handleAvatarUpload = async (file: File) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return false
  }
  
  // 检查文件大小
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB')
    return false
  }
  
  try {
    avatarLoading.value = true
    
    // 模拟API调用
    setTimeout(() => {
      // 更新头像URL
      userProfile.value.avatar_url = URL.createObjectURL(file)
      
      ElMessage.success('头像上传成功')
      avatarLoading.value = false
    }, 1000)
    
    // 实际API调用应该类似于:
    // const formData = new FormData()
    // formData.append('avatar', file)
    // const response = await userService.uploadAvatar(file)
    // userProfile.value.avatar_url = response.avatar_url
  } catch (error) {
    console.error('Failed to upload avatar:', error)
    ElMessage.error('头像上传失败')
    avatarLoading.value = false
  }
  
  return false
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

onMounted(() => {
  fetchUserProfile()
})
</script>

<template>
  <div class="profile-container" v-loading="profileLoading">
    <div class="page-header">
      <h1>个人资料</h1>
    </div>
    
    <el-row :gutter="20">
      <el-col :xs="24" :md="8">
        <!-- 用户信息卡片 -->
        <el-card class="profile-card">
          <div class="avatar-container">
            <el-avatar :src="userProfile.avatar_url" :size="100" />
            <div class="avatar-upload">
              <el-upload
                class="avatar-uploader"
                action="#"
                :show-file-list="false"
                :before-upload="handleAvatarUpload"
                :disabled="avatarLoading"
              >
                <el-button type="primary" :loading="avatarLoading">更换头像</el-button>
              </el-upload>
            </div>
          </div>
          
          <div class="user-info">
            <h2>{{ userProfile.username }}</h2>
            <p class="user-role">{{ userProfile.role === 'tenant' ? '租客' : userProfile.role === 'landlord' ? '房东' : '管理员' }}</p>
            
            <div class="info-item">
              <span class="label">注册时间:</span>
              <span class="value">{{ userProfile.registration_date }}</span>
            </div>
            
            <div class="info-item">
              <span class="label">最后登录:</span>
              <span class="value">{{ formatDateTime(userProfile.last_login_at) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :md="16">
        <!-- 个人资料表单 -->
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <h2>基本信息</h2>
            </div>
          </template>
          
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
          >
            <el-form-item label="用户名">
              <el-input v-model="userProfile.username" disabled />
            </el-form-item>
            
            <el-form-item label="姓名" prop="full_name">
              <el-input v-model="profileForm.full_name" />
            </el-form-item>
            
            <el-form-item label="电话号码" prop="phone_number">
              <el-input v-model="profileForm.phone_number" />
            </el-form-item>
            
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="profileForm.email" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="updateProfile(profileFormRef)" :loading="profileLoading">
                保存修改
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
        
        <!-- 修改密码表单 -->
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <h2>修改密码</h2>
            </div>
          </template>
          
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <el-form-item label="当前密码" prop="old_password">
              <el-input v-model="passwordForm.old_password" type="password" show-password />
            </el-form-item>
            
            <el-form-item label="新密码" prop="new_password">
              <el-input v-model="passwordForm.new_password" type="password" show-password />
            </el-form-item>
            
            <el-form-item label="确认新密码" prop="confirm_password">
              <el-input v-model="passwordForm.confirm_password" type="password" show-password />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="updatePassword(passwordFormRef)" :loading="passwordLoading">
                修改密码
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.profile-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.profile-card, .form-card {
  margin-bottom: 20px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.avatar-upload {
  margin-top: 15px;
}

.user-info {
  text-align: center;
}

.user-info h2 {
  margin: 0 0 10px 0;
  font-size: 20px;
  color: #303133;
}

.user-role {
  color: #909399;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.label {
  color: #606266;
}

.value {
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}
</style>
