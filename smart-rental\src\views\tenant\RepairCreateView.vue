<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import leaseService from '@/services/lease.service'
import repairService from '@/services/repair.service'
import type { Lease } from '@/services/lease.service'

const router = useRouter()

// 表单引用
const repairFormRef = ref<FormInstance>()

// 表单数据
const repairForm = reactive({
  house_id: undefined as number | undefined,
  description: '',
  urgency_level: '一般'
})

// 表单验证规则
const rules = reactive<FormRules>({
  house_id: [
    { required: true, message: '请选择房屋', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入问题描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度应为10-500个字符', trigger: 'blur' }
  ],
  urgency_level: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ]
})

// 紧急程度选项
const urgencyOptions = [
  { value: '紧急', label: '紧急' },
  { value: '一般', label: '一般' },
  { value: '低', label: '低' }
]

// 租约列表
const leases = ref<Lease[]>([])
const loading = ref(true)

// 上传的图片列表
const imageList = ref<File[]>([])
const imageUrls = ref<string[]>([])
const uploadLoading = ref(false)

// 获取租约列表
const fetchLeases = async () => {
  try {
    loading.value = true

    const response = await leaseService.getLeaseList({
      status: '生效中'
    })

    leases.value = response.items
  } catch (error) {
    console.error('Failed to fetch leases:', error)
    ElMessage.error('获取租约列表失败')
  } finally {
    loading.value = false
  }
}

// 处理图片上传
const handleImageUpload = (file: File) => {
  // 检查文件类型
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    ElMessage.error('只能上传图片文件')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB')
    return false
  }

  // 添加到图片列表
  imageList.value.push(file)
  imageUrls.value.push(URL.createObjectURL(file))

  return false
}

// 移除图片
const removeImage = (index: number) => {
  imageList.value.splice(index, 1)
  imageUrls.value.splice(index, 1)
}

// 提交表单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return

  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 1. 创建维修请求
        const response = await repairService.createRepair({
          house_id: repairForm.house_id!,
          description: repairForm.description,
          urgency_level: repairForm.urgency_level
        })

        // 2. 上传图片
        if (imageList.value.length > 0) {
          await repairService.uploadRepairImages(response.id, imageList.value)
        }

        ElMessage.success('维修请求已提交')

        // 跳转到维修请求列表页
        router.push('/tenant/repairs')
      } catch (error) {
        console.error('Failed to submit repair request:', error)
        ElMessage.error('提交维修请求失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 取消
const cancel = () => {
  router.push('/tenant/repairs')
}

onMounted(() => {
  fetchLeases()
})
</script>

<template>
  <div class="repair-create-container">
    <div class="page-header">
      <h1>新建维修请求</h1>
    </div>

    <el-card class="form-card" v-loading="loading">
      <el-form
        ref="repairFormRef"
        :model="repairForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="选择房屋" prop="house_id">
          <el-select v-model="repairForm.house_id" placeholder="请选择房屋" style="width: 100%">
            <el-option
              v-for="lease in leases"
              :key="lease.house.id"
              :label="`${lease.house.title} (${lease.house.address})`"
              :value="lease.house.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="问题描述" prop="description">
          <el-input
            v-model="repairForm.description"
            type="textarea"
            :rows="5"
            placeholder="请详细描述您遇到的问题，以便我们更好地为您提供服务"
          />
        </el-form-item>

        <el-form-item label="紧急程度" prop="urgency_level">
          <el-radio-group v-model="repairForm.urgency_level">
            <el-radio
              v-for="option in urgencyOptions"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="上传图片">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :show-file-list="false"
            :before-upload="handleImageUpload"
            :disabled="uploadLoading"
          >
            <el-icon><plus /></el-icon>
          </el-upload>

          <div class="image-preview" v-if="imageUrls.length > 0">
            <div v-for="(url, index) in imageUrls" :key="index" class="image-item">
              <img :src="url" :alt="`图片 ${index + 1}`">
              <div class="image-actions">
                <el-button type="danger" circle @click="removeImage(index)">
                  <el-icon><delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>

          <div class="upload-tip">
            <p>提示: 可上传多张图片，每张图片不超过5MB，支持jpg、png、jpeg格式</p>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm(repairFormRef)" :loading="loading">
            提交
          </el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.repair-create-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.form-card {
  margin-bottom: 20px;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.image-item {
  position: relative;
  width: 148px;
  height: 148px;
  border: 1px solid #e6e6e6;
  border-radius: 6px;
  overflow: hidden;
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 5px;
  right: 5px;
}

.upload-tip {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
