<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 用户信息
const user = ref(authStore.user)

// 统计数据（实际应用中应从API获取）
const stats = ref({
  favoriteCount: 0,
  appointmentCount: 0,
  leaseCount: 0,
  repairCount: 0
})

// 最近预约（实际应用中应从API获取）
const recentAppointments = ref([
  {
    id: 1,
    house: {
      id: 1,
      title: '阳光花园两居室',
      address: '北京市朝阳区阳光花园小区',
      main_image_url: 'https://via.placeholder.com/300x200'
    },
    appointment_time: '2023-06-15T14:00:00',
    status: '已确认'
  },
  {
    id: 2,
    house: {
      id: 2,
      title: '海淀区精装三居室',
      address: '北京市海淀区中关村南大街',
      main_image_url: 'https://via.placeholder.com/300x200'
    },
    appointment_time: '2023-06-18T10:30:00',
    status: '待确认'
  }
])

// 最近租约（实际应用中应从API获取）
const recentLeases = ref([
  {
    id: 1,
    house: {
      id: 1,
      title: '阳光花园两居室',
      address: '北京市朝阳区阳光花园小区',
      main_image_url: 'https://via.placeholder.com/300x200'
    },
    start_date: '2023-01-01',
    end_date: '2024-01-01',
    rent_amount_monthly: 5000,
    status: '生效中'
  }
])

// 获取统计数据
const fetchStats = async () => {
  // 实际应用中应调用API
  stats.value = {
    favoriteCount: 5,
    appointmentCount: 2,
    leaseCount: 1,
    repairCount: 0
  }
}

// 跳转到详情页
const goToDetail = (path: string) => {
  router.push(path)
}

onMounted(() => {
  fetchStats()
})
</script>

<template>
  <div class="dashboard-container">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <h1>欢迎回来，{{ user?.username }}</h1>
      <p>这是您的租客控制台，您可以在这里管理您的租房相关事务。</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><star /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.favoriteCount }}</div>
              <div class="stat-label">收藏房源</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/tenant/favorites')">查看详情</el-button>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><calendar /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.appointmentCount }}</div>
              <div class="stat-label">看房预约</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/tenant/appointments')">查看详情</el-button>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><document /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.leaseCount }}</div>
              <div class="stat-label">我的租约</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/tenant/leases')">查看详情</el-button>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><service /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.repairCount }}</div>
              <div class="stat-label">维修请求</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/tenant/repairs')">查看详情</el-button>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近预约 -->
    <el-card class="recent-section">
      <template #header>
        <div class="card-header">
          <h2>最近预约</h2>
          <el-button text @click="goToDetail('/tenant/appointments')">查看全部</el-button>
        </div>
      </template>
      
      <el-empty v-if="recentAppointments.length === 0" description="暂无预约记录" />
      
      <el-table v-else :data="recentAppointments" style="width: 100%">
        <el-table-column label="房源信息">
          <template #default="{ row }">
            <div class="house-info">
              <el-image
                :src="row.house.main_image_url"
                :alt="row.house.title"
                class="house-image"
                fit="cover"
              />
              <div>
                <div class="house-title">{{ row.house.title }}</div>
                <div class="house-address">{{ row.house.address }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="appointment_time" label="预约时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.appointment_time).toLocaleString() }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.status === '已确认' ? 'success' : row.status === '待确认' ? 'warning' : 'info'"
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              text
              @click="goToDetail(`/tenant/appointments/${row.id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 我的租约 -->
    <el-card class="recent-section">
      <template #header>
        <div class="card-header">
          <h2>我的租约</h2>
          <el-button text @click="goToDetail('/tenant/leases')">查看全部</el-button>
        </div>
      </template>
      
      <el-empty v-if="recentLeases.length === 0" description="暂无租约记录" />
      
      <el-table v-else :data="recentLeases" style="width: 100%">
        <el-table-column label="房源信息">
          <template #default="{ row }">
            <div class="house-info">
              <el-image
                :src="row.house.main_image_url"
                :alt="row.house.title"
                class="house-image"
                fit="cover"
              />
              <div>
                <div class="house-title">{{ row.house.title }}</div>
                <div class="house-address">{{ row.house.address }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="租期" width="200">
          <template #default="{ row }">
            {{ row.start_date }} 至 {{ row.end_date }}
          </template>
        </el-table-column>
        
        <el-table-column prop="rent_amount_monthly" label="月租金" width="120">
          <template #default="{ row }">
            ¥{{ row.rent_amount_monthly }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.status === '生效中' ? 'success' : row.status === '待签署' ? 'warning' : 'info'"
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              text
              @click="goToDetail(`/tenant/leases/${row.id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-section h1 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #303133;
}

.welcome-section p {
  margin: 0;
  color: #606266;
}

.stat-cards {
  margin-bottom: 30px;
}

.stat-card {
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.stat-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  color: #909399;
}

.recent-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-image {
  width: 60px;
  height: 40px;
  margin-right: 10px;
  border-radius: 4px;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.house-address {
  font-size: 12px;
  color: #909399;
}
</style>
