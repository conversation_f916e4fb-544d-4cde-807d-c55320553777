<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 收款记录列表数据
const payments = ref([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  payment_method: '',
  start_date: '',
  end_date: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '待支付', label: '待支付' },
  { value: '已支付', label: '已支付' },
  { value: '逾期', label: '逾期' },
  { value: '已退款', label: '已退款' }
]

// 支付方式选项
const paymentMethodOptions = [
  { value: '', label: '全部' },
  { value: '银行转账', label: '银行转账' },
  { value: '支付宝', label: '支付宝' },
  { value: '微信支付', label: '微信支付' },
  { value: '现金', label: '现金' }
]

// 获取收款记录列表
const fetchPayments = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    const mockPayments = [
      {
        id: 1,
        lease: {
          id: 1,
          house: {
            title: '阳光花园精装两居室',
            address: '朝阳区阳光花园小区3号楼2单元101室'
          },
          tenant: {
            full_name: '张三',
            phone_number: '13800138001'
          }
        },
        amount: 3500,
        payment_type: '月租金',
        due_date: '2024-01-01',
        payment_date: '2023-12-28',
        status: '已支付',
        payment_method: '银行转账',
        created_at: '2023-12-01T10:00:00Z'
      },
      {
        id: 2,
        lease: {
          id: 2,
          house: {
            title: '温馨一居室',
            address: '海淀区中关村大街1号'
          },
          tenant: {
            full_name: '李四',
            phone_number: '13800138002'
          }
        },
        amount: 2800,
        payment_type: '月租金',
        due_date: '2024-01-05',
        payment_date: null,
        status: '待支付',
        payment_method: null,
        created_at: '2023-12-05T10:00:00Z'
      }
    ]
    
    payments.value = mockPayments
    total.value = mockPayments.length
  } catch (error) {
    console.error('Failed to fetch payments:', error)
    ElMessage.error('获取收款记录失败')
  } finally {
    loading.value = false
  }
}

// 确认收款
const confirmPayment = async (paymentId: number) => {
  try {
    // 模拟API调用
    const index = payments.value.findIndex(item => item.id === paymentId)
    if (index !== -1) {
      payments.value[index].status = '已支付'
      payments.value[index].payment_date = new Date().toISOString()
      payments.value[index].payment_method = '银行转账'
    }
    
    ElMessage.success('收款确认成功')
  } catch (error) {
    console.error('Failed to confirm payment:', error)
    ElMessage.error('确认收款失败')
  }
}

// 查看租约详情
const viewLeaseDetail = (leaseId: number) => {
  router.push(`/landlord/leases/${leaseId}`)
}

// 处理筛选变化
const handleFilterChange = () => {
  filters.page = 1
  fetchPayments()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchPayments()
}

// 格式化日期
const formatDate = (dateStr: string | null) => {
  if (!dateStr) return '暂无'
  return new Date(dateStr).toLocaleDateString()
}

// 计算总收入
const totalIncome = ref(0)
const monthlyIncome = ref(0)

const calculateIncome = () => {
  const paidPayments = payments.value.filter(p => p.status === '已支付')
  totalIncome.value = paidPayments.reduce((sum, p) => sum + p.amount, 0)
  
  const currentMonth = new Date().getMonth()
  const currentYear = new Date().getFullYear()
  monthlyIncome.value = paidPayments
    .filter(p => {
      const paymentDate = new Date(p.payment_date)
      return paymentDate.getMonth() === currentMonth && paymentDate.getFullYear() === currentYear
    })
    .reduce((sum, p) => sum + p.amount, 0)
}

onMounted(() => {
  fetchPayments()
  calculateIncome()
})
</script>

<template>
  <div class="payment-list-container">
    <div class="page-header">
      <h1>收款管理</h1>
    </div>
    
    <!-- 收入统计 -->
    <el-row :gutter="20" class="income-stats">
      <el-col :span="12">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><money /></el-icon>
            <div class="stat-info">
              <div class="stat-value">¥{{ totalIncome }}</div>
              <div class="stat-label">总收入</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><calendar /></el-icon>
            <div class="stat-info">
              <div class="stat-value">¥{{ monthlyIncome }}</div>
              <div class="stat-label">本月收入</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="支付方式">
          <el-select v-model="filters.payment_method" placeholder="选择支付方式" clearable @change="handleFilterChange">
            <el-option
              v-for="item in paymentMethodOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="filters.start_date"
            type="date"
            placeholder="开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleFilterChange"
          />
          <span style="margin: 0 10px;">至</span>
          <el-date-picker
            v-model="filters.end_date"
            type="date"
            placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleFilterChange"
          />
        </el-form-item>
      </el-form>
    </el-card>
    
    <div class="payment-list" v-loading="loading">
      <el-empty v-if="payments.length === 0 && !loading" description="暂无收款记录" />
      
      <el-table :data="payments" style="width: 100%">
        <el-table-column label="房源信息" width="250">
          <template #default="{ row }">
            <div class="house-info">
              <div class="house-title">{{ row.lease.house.title }}</div>
              <div class="house-address">{{ row.lease.house.address }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="租客信息" width="150">
          <template #default="{ row }">
            <div>
              <div class="tenant-name">{{ row.lease.tenant.full_name }}</div>
              <div class="tenant-phone">{{ row.lease.tenant.phone_number }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="payment_type" label="费用类型" width="120" />
        
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="{ row }">
            <span class="amount">¥{{ row.amount }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="due_date" label="应付日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.due_date) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="payment_date" label="实付日期" width="120">
          <template #default="{ row }">
            {{ formatDate(row.payment_date) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === '已支付' ? 'success' :
                row.status === '待支付' ? 'warning' :
                row.status === '逾期' ? 'danger' : 'info'
              "
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="payment_method" label="支付方式" width="120">
          <template #default="{ row }">
            {{ row.payment_method || '暂无' }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              v-if="row.status === '待支付'"
              @click="confirmPayment(row.id)"
            >
              确认收款
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="viewLeaseDetail(row.lease.id)"
            >
              查看租约
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.payment-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.income-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-right: 15px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  color: #909399;
}

.filter-card {
  margin-bottom: 20px;
}

.payment-list {
  min-height: 400px;
}

.house-info {
  display: flex;
  flex-direction: column;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.house-address {
  font-size: 12px;
  color: #909399;
}

.tenant-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.tenant-phone {
  font-size: 12px;
  color: #909399;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
