<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import houseService from '@/services/house.service'
import type { HouseListItem } from '@/services/house.service'

const router = useRouter()

// 房源列表数据
const houses = ref<HouseListItem[]>([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  status: '',
  page: 1,
  per_page: 12
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '可租', label: '可租' },
  { value: '已租', label: '已租' },
  { value: '维护中', label: '维护中' }
]

// 获取房源列表
const fetchHouses = async () => {
  try {
    loading.value = true
    
    const params = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)
    
    const response = await houseService.getHouseList(params)
    houses.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch houses:', error)
    ElMessage.error('获取房源列表失败')
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    page: 1,
    per_page: 12
  })
  
  fetchHouses()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchHouses()
}

// 跳转到房源详情页
const viewHouseDetail = (id: number) => {
  router.push(`/houses/${id}`)
}

// 编辑房源
const editHouse = (id: number) => {
  router.push(`/landlord/houses/${id}/edit`)
}

// 删除房源
const deleteHouse = (id: number) => {
  ElMessageBox.confirm('确定要删除该房源吗？删除后无法恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await houseService.deleteHouse(id)
      
      // 从列表中移除
      houses.value = houses.value.filter(item => item.id !== id)
      total.value--
      
      ElMessage.success('房源已删除')
    } catch (error) {
      console.error('Failed to delete house:', error)
      ElMessage.error('删除房源失败')
    }
  }).catch(() => {})
}

// 创建新房源
const createHouse = () => {
  router.push('/landlord/houses/create')
}

onMounted(() => {
  fetchHouses()
})
</script>

<template>
  <div class="house-list-container">
    <div class="page-header">
      <h1>我的房源</h1>
      <el-button type="primary" @click="createHouse">发布房源</el-button>
    </div>
    
    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" label-width="80px" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="fetchHouses">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 房源列表 -->
    <div class="house-list" v-loading="loading">
      <el-empty v-if="houses.length === 0 && !loading" description="暂无房源，快去发布第一个房源吧！" />
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="house in houses" :key="house.id">
          <el-card class="house-card">
            <div class="house-image" @click="viewHouseDetail(house.id)">
              <img :src="house.main_image_url || 'https://via.placeholder.com/300x200'" :alt="house.title">
              <div class="house-price">¥{{ house.price_monthly }}/月</div>
            </div>
            <div class="house-info">
              <h3 class="house-title" @click="viewHouseDetail(house.id)">
                {{ house.title }}
              </h3>
              <p class="house-address">{{ house.address }}</p>
              <div class="house-tags">
                <el-tag size="small">{{ house.bedrooms }}室{{ house.bathrooms }}卫</el-tag>
                <el-tag size="small">{{ house.area_sqm }}㎡</el-tag>
                <el-tag size="small" :type="house.status === '可租' ? 'success' : house.status === '已租' ? 'info' : 'warning'">
                  {{ house.status }}
                </el-tag>
              </div>
              <div class="house-actions">
                <el-button type="primary" size="small" @click="viewHouseDetail(house.id)">查看</el-button>
                <el-button type="warning" size="small" @click="editHouse(house.id)">编辑</el-button>
                <el-button type="danger" size="small" @click="deleteHouse(house.id)">删除</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.house-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.house-list {
  min-height: 400px;
}

.house-card {
  margin-bottom: 20px;
  transition: transform 0.3s;
}

.house-card:hover {
  transform: translateY(-5px);
}

.house-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  cursor: pointer;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-price {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--el-color-primary);
  color: white;
  padding: 5px 10px;
  font-weight: bold;
}

.house-info {
  padding: 15px;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.house-title:hover {
  color: var(--el-color-primary);
}

.house-address {
  color: #909399;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-tags {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
}

.house-actions {
  display: flex;
  gap: 5px;
}

.pagination-container {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
</style>
