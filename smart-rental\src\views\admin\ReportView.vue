<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 报表数据
const reportData = ref({
  overview: {
    totalUsers: 0,
    totalHouses: 0,
    totalLeases: 0,
    totalRevenue: 0,
    monthlyActiveUsers: 0,
    platformCommission: 0
  },
  userStats: {
    tenants: 0,
    landlords: 0,
    admins: 0,
    newUsersThisMonth: 0
  },
  houseStats: {
    available: 0,
    rented: 0,
    maintenance: 0,
    offline: 0
  },
  leaseStats: {
    active: 0,
    pending: 0,
    expired: 0,
    terminated: 0
  },
  monthlyData: [],
  cityDistribution: [],
  revenueData: []
})

const loading = ref(true)

// 获取报表数据
const fetchReportData = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    const mockData = {
      overview: {
        totalUsers: 1250,
        totalHouses: 856,
        totalLeases: 642,
        totalRevenue: 2580000,
        monthlyActiveUsers: 890,
        platformCommission: 129000
      },
      userStats: {
        tenants: 980,
        landlords: 260,
        admins: 10,
        newUsersThisMonth: 85
      },
      houseStats: {
        available: 214,
        rented: 642,
        maintenance: 0,
        offline: 0
      },
      leaseStats: {
        active: 580,
        pending: 25,
        expired: 32,
        terminated: 5
      },
      monthlyData: [
        { month: '2023-07', users: 1050, houses: 720, leases: 520, revenue: 2100000 },
        { month: '2023-08', users: 1120, houses: 760, leases: 550, revenue: 2250000 },
        { month: '2023-09', users: 1180, houses: 800, leases: 580, revenue: 2380000 },
        { month: '2023-10', users: 1220, houses: 830, leases: 610, revenue: 2450000 },
        { month: '2023-11', users: 1250, houses: 856, leases: 642, revenue: 2580000 },
        { month: '2023-12', users: 1250, houses: 856, leases: 642, revenue: 2580000 }
      ],
      cityDistribution: [
        { city: '北京', houses: 280, percentage: 32.7 },
        { city: '上海', houses: 220, percentage: 25.7 },
        { city: '广州', houses: 150, percentage: 17.5 },
        { city: '深圳', houses: 120, percentage: 14.0 },
        { city: '杭州', houses: 56, percentage: 6.5 },
        { city: '南京', houses: 30, percentage: 3.5 }
      ],
      revenueData: [
        { month: '2023-07', totalRevenue: 2100000, commission: 105000 },
        { month: '2023-08', totalRevenue: 2250000, commission: 112500 },
        { month: '2023-09', totalRevenue: 2380000, commission: 119000 },
        { month: '2023-10', totalRevenue: 2450000, commission: 122500 },
        { month: '2023-11', totalRevenue: 2580000, commission: 129000 },
        { month: '2023-12', totalRevenue: 2580000, commission: 129000 }
      ]
    }
    
    reportData.value = mockData
  } catch (error) {
    console.error('Failed to fetch report data:', error)
  } finally {
    loading.value = false
  }
}

// 导出报表
const exportReport = () => {
  // 模拟导出功能
  const data = JSON.stringify(reportData.value, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `系统报表_${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
}

onMounted(() => {
  fetchReportData()
})
</script>

<template>
  <div class="report-container" v-loading="loading">
    <div class="page-header">
      <h1>系统报表</h1>
      <el-button type="primary" @click="exportReport">导出报表</el-button>
    </div>
    
    <!-- 概览统计 -->
    <div class="overview-section">
      <h2>平台概览</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon"><user /></el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ reportData.overview.totalUsers.toLocaleString() }}</div>
                <div class="stat-label">总用户数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon house-icon"><house /></el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ reportData.overview.totalHouses.toLocaleString() }}</div>
                <div class="stat-label">总房源数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon lease-icon"><document /></el-icon>
              <div class="stat-info">
                <div class="stat-value">{{ reportData.overview.totalLeases.toLocaleString() }}</div>
                <div class="stat-label">总租约数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <el-icon class="stat-icon revenue-icon"><money /></el-icon>
              <div class="stat-info">
                <div class="stat-value">¥{{ (reportData.overview.totalRevenue / 10000).toFixed(1) }}万</div>
                <div class="stat-label">总交易额</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 用户统计 -->
    <div class="user-section">
      <h2>用户统计</h2>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card>
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="user-stat">
                  <div class="user-value">{{ reportData.userStats.tenants }}</div>
                  <div class="user-label">租客</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="user-stat">
                  <div class="user-value">{{ reportData.userStats.landlords }}</div>
                  <div class="user-label">房东</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="user-stat">
                  <div class="user-value">{{ reportData.userStats.admins }}</div>
                  <div class="user-label">管理员</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="user-stat">
                  <div class="user-value">{{ reportData.userStats.newUsersThisMonth }}</div>
                  <div class="user-label">本月新增</div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        
        <el-col :span="8">
          <el-card>
            <div class="active-users">
              <div class="active-value">{{ reportData.overview.monthlyActiveUsers }}</div>
              <div class="active-label">月活跃用户</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 房源统计 -->
    <div class="house-section">
      <h2>房源统计</h2>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="house-stat available">
                  <div class="house-value">{{ reportData.houseStats.available }}</div>
                  <div class="house-label">可租</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="house-stat rented">
                  <div class="house-value">{{ reportData.houseStats.rented }}</div>
                  <div class="house-label">已租</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="house-stat maintenance">
                  <div class="house-value">{{ reportData.houseStats.maintenance }}</div>
                  <div class="house-label">维护中</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="house-stat offline">
                  <div class="house-value">{{ reportData.houseStats.offline }}</div>
                  <div class="house-label">已下架</div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card title="城市分布">
            <el-table :data="reportData.cityDistribution" style="width: 100%">
              <el-table-column prop="city" label="城市" width="80" />
              <el-table-column prop="houses" label="房源数" width="80" />
              <el-table-column prop="percentage" label="占比" width="80">
                <template #default="{ row }">
                  {{ row.percentage }}%
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
    
    <!-- 租约统计 -->
    <div class="lease-section">
      <h2>租约统计</h2>
      <el-card>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="lease-stat active">
              <div class="lease-value">{{ reportData.leaseStats.active }}</div>
              <div class="lease-label">生效中</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="lease-stat pending">
              <div class="lease-value">{{ reportData.leaseStats.pending }}</div>
              <div class="lease-label">待签署</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="lease-stat expired">
              <div class="lease-value">{{ reportData.leaseStats.expired }}</div>
              <div class="lease-label">已到期</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="lease-stat terminated">
              <div class="lease-value">{{ reportData.leaseStats.terminated }}</div>
              <div class="lease-label">已解除</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    
    <!-- 收入统计 -->
    <div class="revenue-section">
      <h2>收入统计</h2>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div class="revenue-item">
              <span class="revenue-label">平台佣金收入</span>
              <span class="revenue-value">¥{{ reportData.overview.platformCommission.toLocaleString() }}</span>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="12">
          <el-card title="月度收入趋势">
            <el-table :data="reportData.revenueData.slice(-3)" style="width: 100%">
              <el-table-column prop="month" label="月份" width="100" />
              <el-table-column prop="totalRevenue" label="总交易额" width="120">
                <template #default="{ row }">
                  ¥{{ (row.totalRevenue / 10000).toFixed(1) }}万
                </template>
              </el-table-column>
              <el-table-column prop="commission" label="佣金收入" width="120">
                <template #default="{ row }">
                  ¥{{ row.commission.toLocaleString() }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.report-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.overview-section,
.user-section,
.house-section,
.lease-section,
.revenue-section {
  margin-bottom: 30px;
}

.overview-section h2,
.user-section h2,
.house-section h2,
.lease-section h2,
.revenue-section h2 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #303133;
}

.stat-card {
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-right: 15px;
}

.house-icon {
  color: #67c23a;
}

.lease-icon {
  color: #e6a23c;
}

.revenue-icon {
  color: #f56c6c;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  color: #909399;
}

.user-stat,
.house-stat,
.lease-stat {
  text-align: center;
  padding: 20px 0;
}

.user-value,
.house-value,
.lease-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.user-label,
.house-label,
.lease-label {
  color: #909399;
}

.house-stat.available .house-value {
  color: #67c23a;
}

.house-stat.rented .house-value {
  color: #409eff;
}

.house-stat.maintenance .house-value {
  color: #e6a23c;
}

.house-stat.offline .house-value {
  color: #f56c6c;
}

.lease-stat.active .lease-value {
  color: #67c23a;
}

.lease-stat.pending .lease-value {
  color: #e6a23c;
}

.lease-stat.expired .lease-value {
  color: #909399;
}

.lease-stat.terminated .lease-value {
  color: #f56c6c;
}

.active-users {
  text-align: center;
  padding: 20px 0;
}

.active-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 10px;
}

.active-label {
  color: #909399;
}

.revenue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.revenue-label {
  font-size: 16px;
  color: #606266;
}

.revenue-value {
  font-size: 24px;
  font-weight: bold;
  color: #f56c6c;
}
</style>
