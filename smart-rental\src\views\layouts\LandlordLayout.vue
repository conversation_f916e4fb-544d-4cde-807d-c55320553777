<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏折叠状态
const isCollapse = ref(false)

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 未读消息数量（实际应用中应从API获取）
const unreadMessageCount = ref(0)

// 检查用户是否已登录且是房东
onMounted(() => {
  if (!authStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  if (!authStore.isLandlord) {
    router.push('/')
    return
  }
})
</script>

<template>
  <div class="landlord-layout">
    <AppHeader />
    
    <div class="main-container">
      <el-container>
        <!-- 侧边栏 -->
        <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
          <div class="sidebar-header">
            <h3 v-if="!isCollapse">房东中心</h3>
            <el-icon class="collapse-btn" @click="isCollapse = !isCollapse">
              <fold v-if="!isCollapse" />
              <expand v-else />
            </el-icon>
          </div>
          
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            :collapse="isCollapse"
            :router="true"
          >
            <el-menu-item index="/landlord/dashboard">
              <el-icon><menu /></el-icon>
              <template #title>控制台</template>
            </el-menu-item>
            
            <el-sub-menu index="/landlord/houses">
              <template #title>
                <el-icon><house /></el-icon>
                <span>房源管理</span>
              </template>
              <el-menu-item index="/landlord/houses">我的房源</el-menu-item>
              <el-menu-item index="/landlord/houses/create">发布房源</el-menu-item>
            </el-sub-menu>
            
            <el-menu-item index="/landlord/appointments">
              <el-icon><calendar /></el-icon>
              <template #title>看房预约</template>
            </el-menu-item>
            
            <el-sub-menu index="/landlord/leases">
              <template #title>
                <el-icon><document /></el-icon>
                <span>租约管理</span>
              </template>
              <el-menu-item index="/landlord/leases">租约列表</el-menu-item>
              <el-menu-item index="/landlord/leases/create">创建租约</el-menu-item>
            </el-sub-menu>
            
            <el-menu-item index="/landlord/repairs">
              <el-icon><service /></el-icon>
              <template #title>维修管理</template>
            </el-menu-item>
            
            <el-menu-item index="/landlord/payments">
              <el-icon><money /></el-icon>
              <template #title>收款管理</template>
            </el-menu-item>
            
            <el-menu-item index="/landlord/reports">
              <el-icon><data-analysis /></el-icon>
              <template #title>数据报表</template>
            </el-menu-item>
            
            <el-menu-item index="/landlord/messages">
              <el-icon><chat-dot-round /></el-icon>
              <template #title>
                我的消息
                <el-badge v-if="unreadMessageCount > 0" :value="unreadMessageCount" class="message-badge" />
              </template>
            </el-menu-item>
            
            <el-menu-item index="/landlord/profile">
              <el-icon><user /></el-icon>
              <template #title>个人资料</template>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- 主内容区 -->
        <el-container>
          <el-main class="main-content">
            <router-view />
          </el-main>
        </el-container>
      </el-container>
    </div>
    
    <AppFooter />
  </div>
</template>

<style scoped>
.landlord-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  background-color: #f5f7fa;
}

.sidebar {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: width 0.3s;
  overflow: hidden;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #e6e6e6;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.collapse-btn {
  cursor: pointer;
  font-size: 20px;
}

.sidebar-menu {
  border-right: none;
}

.main-content {
  padding: 20px;
  background-color: #f5f7fa;
}

.message-badge {
  margin-left: 8px;
}
</style>
