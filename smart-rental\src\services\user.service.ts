import api from './api'

export interface UserProfile {
  id: number
  username: string
  email: string
  role: string
  full_name: string
  phone_number: string
  avatar_url?: string
  registration_date: string
  last_login_at: string
}

export interface UpdateUserRequest {
  full_name?: string
  phone_number?: string
  email?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  per_page: number
  pages: number
}

export interface UserListItem {
  id: number
  username: string
  email: string
  role: string
  full_name: string
}

class UserService {
  /**
   * 获取当前用户信息
   */
  async getCurrentUserProfile(): Promise<UserProfile> {
    const response = await api.get<UserProfile>('/users/me')
    return response.data
  }

  /**
   * 更新用户信息
   * @param data 更新的用户信息
   */
  async updateUserProfile(data: UpdateUserRequest): Promise<UserProfile> {
    const response = await api.put<UserProfile>('/users/me', data)
    return response.data
  }

  /**
   * 上传用户头像
   * @param file 头像文件
   */
  async uploadAvatar(file: File): Promise<{ avatar_url: string }> {
    const formData = new FormData()
    formData.append('avatar', file)

    const response = await api.post<{ avatar_url: string }>('/users/me/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data
  }

  /**
   * 获取用户列表 (仅管理员)
   * @param params 查询参数
   */
  async getUserList(params?: {
    page?: number
    per_page?: number
    role?: string
    search?: string
  }): Promise<UserListItem[]> {
    const response = await api.get<UserListItem[]>('/users', { params })
    return response.data
  }
}

export default new UserService()
