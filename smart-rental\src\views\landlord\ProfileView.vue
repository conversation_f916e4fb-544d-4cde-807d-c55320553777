<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import userService from '@/services/user.service'

const authStore = useAuthStore()

// 表单引用
const profileFormRef = ref<FormInstance>()

// 表单数据
const profileForm = reactive({
  username: '',
  full_name: '',
  email: '',
  phone_number: '',
  id_card_number: '',
  address: '',
  bank_account: '',
  bank_name: ''
})

// 密码修改表单
const passwordForm = reactive({
  current_password: '',
  new_password: '',
  confirm_password: ''
})

// 表单验证规则
const profileRules = reactive<FormRules>({
  full_name: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone_number: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
})

const passwordRules = reactive<FormRules>({
  current_password: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  new_password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.new_password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

const loading = ref(false)
const passwordDialogVisible = ref(false)

// 获取用户信息
const fetchUserProfile = async () => {
  try {
    loading.value = true
    const user = authStore.user
    if (user) {
      Object.assign(profileForm, {
        username: user.username,
        full_name: user.full_name,
        email: user.email,
        phone_number: user.phone_number,
        id_card_number: user.id_card_number || '',
        address: user.address || '',
        bank_account: user.bank_account || '',
        bank_name: user.bank_name || ''
      })
    }
  } catch (error) {
    console.error('Failed to fetch user profile:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 更新个人信息
const updateProfile = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        
        await userService.updateProfile(profileForm)
        
        // 更新store中的用户信息
        authStore.updateUser(profileForm)
        
        ElMessage.success('个人信息更新成功')
      } catch (error) {
        console.error('Failed to update profile:', error)
        ElMessage.error('更新个人信息失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 修改密码
const changePassword = async () => {
  try {
    await userService.changePassword(passwordForm)
    
    ElMessage.success('密码修改成功')
    passwordDialogVisible.value = false
    
    // 重置密码表单
    Object.assign(passwordForm, {
      current_password: '',
      new_password: '',
      confirm_password: ''
    })
  } catch (error) {
    console.error('Failed to change password:', error)
    ElMessage.error('密码修改失败')
  }
}

onMounted(() => {
  fetchUserProfile()
})
</script>

<template>
  <div class="profile-container">
    <div class="page-header">
      <h1>个人资料</h1>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card title="基本信息" v-loading="loading">
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户名">
                  <el-input v-model="profileForm.username" disabled />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="真实姓名" prop="full_name">
                  <el-input v-model="profileForm.full_name" placeholder="请输入真实姓名" />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="profileForm.email" placeholder="请输入邮箱" />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="手机号" prop="phone_number">
                  <el-input v-model="profileForm.phone_number" placeholder="请输入手机号" />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="身份证号">
                  <el-input v-model="profileForm.id_card_number" placeholder="请输入身份证号" />
                </el-form-item>
              </el-col>
              
              <el-col :span="24">
                <el-form-item label="地址">
                  <el-input v-model="profileForm.address" placeholder="请输入详细地址" />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="银行账号">
                  <el-input v-model="profileForm.bank_account" placeholder="请输入银行账号" />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="开户银行">
                  <el-input v-model="profileForm.bank_name" placeholder="请输入开户银行" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button type="primary" @click="updateProfile(profileFormRef)" :loading="loading">
                保存修改
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card title="账户安全">
          <div class="security-item">
            <div class="security-label">登录密码</div>
            <div class="security-value">已设置</div>
            <el-button text @click="passwordDialogVisible = true">修改</el-button>
          </div>
          
          <div class="security-item">
            <div class="security-label">手机绑定</div>
            <div class="security-value">{{ profileForm.phone_number || '未绑定' }}</div>
          </div>
          
          <div class="security-item">
            <div class="security-label">邮箱绑定</div>
            <div class="security-value">{{ profileForm.email || '未绑定' }}</div>
          </div>
        </el-card>
        
        <el-card title="账户统计" style="margin-top: 20px;">
          <div class="stat-item">
            <div class="stat-label">注册时间</div>
            <div class="stat-value">{{ authStore.user?.registration_date || '未知' }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">最后登录</div>
            <div class="stat-value">{{ authStore.user?.last_login || '未知' }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">账户状态</div>
            <div class="stat-value">
              <el-tag type="success">正常</el-tag>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="500px"
    >
      <el-form
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="current_password">
          <el-input
            v-model="passwordForm.current_password"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="new_password">
          <el-input
            v-model="passwordForm.new_password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirm_password">
          <el-input
            v-model="passwordForm.confirm_password"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="changePassword">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.profile-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child {
  border-bottom: none;
}

.security-label {
  font-weight: bold;
  color: #303133;
}

.security-value {
  color: #606266;
  flex: 1;
  margin: 0 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #303133;
  font-weight: bold;
}
</style>
