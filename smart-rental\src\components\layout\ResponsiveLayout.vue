<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  title?: string
  showBackButton?: boolean
  backPath?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBackButton: false,
  backPath: '/'
})

// 响应式断点
const isMobile = ref(false)
const isTablet = ref(false)
const isDesktop = ref(true)

// 检查屏幕尺寸
const checkScreenSize = () => {
  const width = window.innerWidth
  
  isMobile.value = width < 768
  isTablet.value = width >= 768 && width < 1024
  isDesktop.value = width >= 1024
}

// 监听窗口大小变化
const handleResize = () => {
  checkScreenSize()
}

onMounted(() => {
  checkScreenSize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 暴露响应式状态给父组件
defineExpose({
  isMobile,
  isTablet,
  isDesktop
})
</script>

<template>
  <div class="responsive-layout" :class="{ 
    'is-mobile': isMobile, 
    'is-tablet': isTablet, 
    'is-desktop': isDesktop 
  }">
    <!-- 页面头部 -->
    <div v-if="title || showBackButton" class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button 
            v-if="showBackButton" 
            :icon="ArrowLeft" 
            @click="$router.push(backPath)"
            class="back-button"
          >
            <span v-if="!isMobile">返回</span>
          </el-button>
          <h1 v-if="title" class="page-title">{{ title }}</h1>
        </div>
        <div class="header-right">
          <slot name="header-actions" />
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <slot />
    </div>

    <!-- 底部操作区域 -->
    <div v-if="$slots.footer" class="page-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<style scoped>
.responsive-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  padding: 8px 12px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.page-content {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.page-footer {
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 20px;
  position: sticky;
  bottom: 0;
}

/* 移动端适配 */
.is-mobile .page-header {
  padding: 12px 16px;
}

.is-mobile .page-title {
  font-size: 18px;
}

.is-mobile .page-content {
  padding: 16px;
}

.is-mobile .page-footer {
  padding: 12px 16px;
}

.is-mobile .header-content {
  gap: 12px;
}

.is-mobile .header-left {
  gap: 12px;
}

/* 平板适配 */
.is-tablet .page-content {
  padding: 20px 24px;
}

/* 桌面端适配 */
.is-desktop .page-content {
  padding: 24px 32px;
}

/* 响应式网格系统 */
:deep(.responsive-grid) {
  display: grid;
  gap: 20px;
}

:deep(.responsive-grid.cols-1) {
  grid-template-columns: 1fr;
}

:deep(.responsive-grid.cols-2) {
  grid-template-columns: repeat(2, 1fr);
}

:deep(.responsive-grid.cols-3) {
  grid-template-columns: repeat(3, 1fr);
}

:deep(.responsive-grid.cols-4) {
  grid-template-columns: repeat(4, 1fr);
}

/* 移动端强制单列 */
.is-mobile :deep(.responsive-grid) {
  grid-template-columns: 1fr !important;
  gap: 16px;
}

/* 平板端最多两列 */
.is-tablet :deep(.responsive-grid.cols-3),
.is-tablet :deep(.responsive-grid.cols-4) {
  grid-template-columns: repeat(2, 1fr);
}

/* 响应式卡片 */
:deep(.responsive-card) {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

:deep(.responsive-card:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

/* 响应式表格 */
.is-mobile :deep(.el-table) {
  font-size: 14px;
}

.is-mobile :deep(.el-table .el-table__cell) {
  padding: 8px 4px;
}

/* 响应式按钮组 */
:deep(.responsive-button-group) {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.is-mobile :deep(.responsive-button-group) {
  flex-direction: column;
}

.is-mobile :deep(.responsive-button-group .el-button) {
  width: 100%;
  margin: 0;
}

/* 响应式表单 */
:deep(.responsive-form) {
  max-width: 600px;
}

.is-mobile :deep(.responsive-form .el-form-item) {
  margin-bottom: 16px;
}

.is-mobile :deep(.responsive-form .el-form-item__label) {
  line-height: 1.4;
  margin-bottom: 8px;
}

/* 响应式间距 */
.is-mobile :deep(.el-row) {
  margin-left: -8px !important;
  margin-right: -8px !important;
}

.is-mobile :deep(.el-col) {
  padding-left: 8px !important;
  padding-right: 8px !important;
}

/* 隐藏/显示工具类 */
.hide-on-mobile {
  display: block;
}

.is-mobile .hide-on-mobile {
  display: none !important;
}

.show-on-mobile {
  display: none;
}

.is-mobile .show-on-mobile {
  display: block !important;
}

.hide-on-tablet {
  display: block;
}

.is-tablet .hide-on-tablet {
  display: none !important;
}

.show-on-tablet {
  display: none;
}

.is-tablet .show-on-tablet {
  display: block !important;
}

.hide-on-desktop {
  display: block;
}

.is-desktop .hide-on-desktop {
  display: none !important;
}

.show-on-desktop {
  display: none;
}

.is-desktop .show-on-desktop {
  display: block !important;
}
</style>
