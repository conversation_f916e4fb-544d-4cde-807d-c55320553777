import api from './api'

export interface Contract {
  id: number
  house: {
    id: number
    title: string
    address: string
    main_image_url: string
  }
  tenant: {
    id: number
    username: string
    full_name: string
    phone_number: string
    email: string
  }
  landlord: {
    id: number
    username: string
    full_name: string
    phone_number: string
    email: string
  }
  start_date: string
  end_date: string
  monthly_rent: number
  deposit_amount: number
  status: string
  contract_url?: string
  tenant_signed: boolean
  landlord_signed: boolean
  created_at: string
  updated_at: string
}

export interface CreateContractRequest {
  house_id: number
  tenant_id: number
  start_date: string
  end_date: string
  monthly_rent: number
  deposit_amount: number
  terms?: string
}

export interface SignContractRequest {
  signature: string
}

export interface RenewContractRequest {
  end_date: string
  monthly_rent?: number
}

export interface MessageResponse {
  message: string
}

class ContractService {
  /**
   * 创建租赁合同
   * @param data 合同信息
   */
  async createContract(data: CreateContractRequest): Promise<{ id: number; message: string }> {
    const response = await api.post<{ id: number; message: string }>('/leases/contracts', data)
    return response.data
  }

  /**
   * 获取合同列表
   * @param params 查询参数
   */
  async getContractList(params?: {
    status?: string
    house_id?: number
    tenant_id?: number
  }): Promise<Contract[]> {
    const response = await api.get<Contract[]>('/leases/contracts', { params })
    return response.data
  }

  /**
   * 获取合同详情
   * @param contractId 合同ID
   */
  async getContractDetail(contractId: number): Promise<Contract> {
    const response = await api.get<Contract>(`/leases/contracts/${contractId}`)
    return response.data
  }

  /**
   * 签署合同
   * @param contractId 合同ID
   * @param data 签署信息
   */
  async signContract(contractId: number, data: SignContractRequest): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>(`/leases/contracts/${contractId}/sign`, data)
    return response.data
  }

  /**
   * 续约合同
   * @param contractId 合同ID
   * @param data 续约信息
   */
  async renewContract(contractId: number, data: RenewContractRequest): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>(`/leases/contracts/${contractId}/renew`, data)
    return response.data
  }

  /**
   * 终止合同
   * @param contractId 合同ID
   * @param reason 终止原因
   */
  async terminateContract(contractId: number, reason?: string): Promise<MessageResponse> {
    const response = await api.post<MessageResponse>(`/leases/contracts/${contractId}/terminate`, { reason })
    return response.data
  }
}

export default new ContractService()
