<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import houseService from '@/services/house.service'
import type { HouseListItem } from '@/services/house.service'
import ResponsiveLayout from '@/components/layout/ResponsiveLayout.vue'

const router = useRouter()

// 房源列表数据
const houses = ref<HouseListItem[]>([])
const loading = ref(true)

// 筛选条件
const filters = ref({
  status: '',
  search: ''
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '待审核', value: 'pending' },
  { label: '已发布', value: 'published' },
  { label: '已出租', value: 'rented' },
  { label: '已下架', value: 'offline' }
]

// 获取我的房源列表
const fetchMyHouses = async () => {
  try {
    loading.value = true
    const response = await houseService.getMyHouses()
    houses.value = response
  } catch (error) {
    console.error('Failed to fetch my houses:', error)
    ElMessage.error('获取房源列表失败')
    houses.value = []
  } finally {
    loading.value = false
  }
}

// 搜索房源
const handleSearch = () => {
  // 这里可以添加本地筛选逻辑
  const filteredHouses = houses.value.filter(house => {
    const matchesStatus = !filters.value.status || house.status === filters.value.status
    const matchesSearch = !filters.value.search || 
      house.title.toLowerCase().includes(filters.value.search.toLowerCase()) ||
      house.address.toLowerCase().includes(filters.value.search.toLowerCase())
    return matchesStatus && matchesSearch
  })
  // 这里应该更新显示的房源列表
}

// 重置筛选
const resetFilters = () => {
  filters.value = {
    status: '',
    search: ''
  }
  fetchMyHouses()
}

// 更新房源状态
const updateHouseStatus = async (houseId: number, status: string) => {
  try {
    await houseService.updateHouseStatus(houseId, status)
    ElMessage.success('状态更新成功')
    fetchMyHouses()
  } catch (error) {
    console.error('Failed to update house status:', error)
    ElMessage.error('状态更新失败')
  }
}

// 删除房源
const deleteHouse = async (houseId: number, title: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除房源"${title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await houseService.deleteHouse(houseId)
    ElMessage.success('房源删除成功')
    fetchMyHouses()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Failed to delete house:', error)
      ElMessage.error('房源删除失败')
    }
  }
}

// 格式化状态
const formatStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待审核',
    'published': '已发布',
    'rented': '已出租',
    'offline': '已下架'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'published': 'success',
    'rented': 'info',
    'offline': 'danger'
  }
  return typeMap[status] || 'info'
}

// 跳转到房源详情
const viewHouse = (houseId: number) => {
  router.push(`/houses/${houseId}`)
}

// 编辑房源
const editHouse = (houseId: number) => {
  router.push(`/landlord/houses/${houseId}/edit`)
}

// 发布新房源
const createHouse = () => {
  router.push('/landlord/houses/create')
}

onMounted(() => {
  fetchMyHouses()
})
</script>

<template>
  <ResponsiveLayout title="我的房源" show-back-button back-path="/landlord/dashboard">
    <template #header-actions>
      <el-button type="primary" @click="createHouse">
        <el-icon><Plus /></el-icon>
        发布房源
      </el-button>
    </template>

    <!-- 筛选条件 -->
    <el-card class="filter-card responsive-card">
      <el-row :gutter="20" align="middle" class="responsive-grid cols-4">
        <el-col :xs="24" :sm="6">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-col>
        
        <el-col :xs="24" :sm="8">
          <el-input
            v-model="filters.search"
            placeholder="搜索房源标题或地址"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        
        <el-col :xs="24" :sm="10">
          <div class="responsive-button-group">
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 房源列表 -->
    <div class="house-grid responsive-grid cols-3">
      <el-card 
        v-for="house in houses" 
        :key="house.id" 
        class="house-card responsive-card"
        shadow="hover"
      >
        <div class="house-image" @click="viewHouse(house.id)">
          <el-image
            :src="house.main_image_url || 'https://via.placeholder.com/300x200'"
            :alt="house.title"
            fit="cover"
            style="width: 100%; height: 200px; cursor: pointer;"
          />
          <div class="house-status">
            <el-tag :type="getStatusType(house.status)" size="small">
              {{ formatStatus(house.status) }}
            </el-tag>
          </div>
        </div>
        
        <div class="house-content">
          <h3 class="house-title" @click="viewHouse(house.id)">{{ house.title }}</h3>
          <p class="house-address">
            <el-icon><Location /></el-icon>
            {{ house.address }}
          </p>
          <div class="house-info">
            <span class="price">¥{{ house.monthly_rent }}/月</span>
            <span class="area">{{ house.area }}㎡</span>
          </div>
        </div>
        
        <div class="house-actions">
          <el-button type="primary" size="small" @click="viewHouse(house.id)">
            查看详情
          </el-button>
          <el-button type="warning" size="small" @click="editHouse(house.id)">
            编辑
          </el-button>
          <el-dropdown @command="(command: string) => updateHouseStatus(house.id, command)">
            <el-button size="small">
              状态 <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="published">发布</el-dropdown-item>
                <el-dropdown-item command="offline">下架</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button 
            type="danger" 
            size="small" 
            @click="deleteHouse(house.id, house.title)"
          >
            删除
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <el-empty v-if="!loading && houses.length === 0" description="暂无房源">
      <el-button type="primary" @click="createHouse">发布第一个房源</el-button>
    </el-empty>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>
  </ResponsiveLayout>
</template>

<style scoped>
.filter-card {
  margin-bottom: 20px;
}

.house-grid {
  gap: 20px;
  margin-bottom: 20px;
}

.house-card {
  overflow: hidden;
  transition: transform 0.3s ease;
}

.house-card:hover {
  transform: translateY(-4px);
}

.house-image {
  position: relative;
  margin: -20px -20px 15px -20px;
}

.house-status {
  position: absolute;
  top: 10px;
  right: 10px;
}

.house-content {
  margin-bottom: 15px;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  cursor: pointer;
  transition: color 0.3s;
}

.house-title:hover {
  color: #409EFF;
}

.house-address {
  margin: 0 0 10px 0;
  color: #909399;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.house-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 18px;
  font-weight: bold;
  color: #E6A23C;
}

.area {
  color: #909399;
  font-size: 14px;
}

.house-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.loading-container {
  padding: 20px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .house-actions {
    flex-direction: column;
  }
  
  .house-actions .el-button {
    width: 100%;
    margin: 0 0 8px 0;
  }
}
</style>
