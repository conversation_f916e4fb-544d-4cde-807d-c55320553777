<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 侧边栏折叠状态
const isCollapse = ref(false)

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 未读消息数量（实际应用中应从API获取）
const unreadMessageCount = ref(0)

// 检查用户是否已登录且是租客
onMounted(() => {
  if (!authStore.isLoggedIn) {
    router.push('/login')
    return
  }
  
  if (!authStore.isTenant) {
    router.push('/')
    return
  }
})
</script>

<template>
  <div class="tenant-layout">
    <AppHeader />
    
    <div class="main-container">
      <el-container>
        <!-- 侧边栏 -->
        <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
          <div class="sidebar-header">
            <h3 v-if="!isCollapse">租客中心</h3>
            <el-icon class="collapse-btn" @click="isCollapse = !isCollapse">
              <fold v-if="!isCollapse" />
              <expand v-else />
            </el-icon>
          </div>
          
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            :collapse="isCollapse"
            :router="true"
          >
            <el-menu-item index="/tenant/dashboard">
              <el-icon><menu /></el-icon>
              <template #title>控制台</template>
            </el-menu-item>
            
            <el-menu-item index="/tenant/favorites">
              <el-icon><star /></el-icon>
              <template #title>我的收藏</template>
            </el-menu-item>
            
            <el-menu-item index="/tenant/appointments">
              <el-icon><calendar /></el-icon>
              <template #title>看房预约</template>
            </el-menu-item>
            
            <el-menu-item index="/tenant/leases">
              <el-icon><document /></el-icon>
              <template #title>我的租约</template>
            </el-menu-item>
            
            <el-menu-item index="/tenant/repairs">
              <el-icon><service /></el-icon>
              <template #title>维修请求</template>
            </el-menu-item>
            
            <el-menu-item index="/tenant/messages">
              <el-icon><chat-dot-round /></el-icon>
              <template #title>
                我的消息
                <el-badge v-if="unreadMessageCount > 0" :value="unreadMessageCount" class="message-badge" />
              </template>
            </el-menu-item>
            
            <el-menu-item index="/tenant/profile">
              <el-icon><user /></el-icon>
              <template #title>个人资料</template>
            </el-menu-item>
          </el-menu>
        </el-aside>
        
        <!-- 主内容区 -->
        <el-container>
          <el-main class="main-content">
            <router-view />
          </el-main>
        </el-container>
      </el-container>
    </div>
    
    <AppFooter />
  </div>
</template>

<style scoped>
.tenant-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  background-color: #f5f7fa;
}

.sidebar {
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: width 0.3s;
  overflow: hidden;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #e6e6e6;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.collapse-btn {
  cursor: pointer;
  font-size: 20px;
}

.sidebar-menu {
  border-right: none;
}

.main-content {
  padding: 20px;
  background-color: #f5f7fa;
}

.message-badge {
  margin-left: 8px;
}
</style>
