<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import houseService from '@/services/house.service'
import type { HouseDetail } from '@/services/house.service'

const route = useRoute()
const router = useRouter()

const house = ref<HouseDetail | null>(null)
const loading = ref(true)
const currentImageIndex = ref(0)

// 获取房源详情
const fetchHouseDetail = async () => {
  try {
    loading.value = true
    const houseId = Number(route.params.id)

    if (isNaN(houseId)) {
      ElMessage.error('无效的房源ID')
      router.push('/houses')
      return
    }

    house.value = await houseService.getHouseDetail(houseId)
  } catch (error) {
    console.error('Failed to fetch house detail:', error)
    ElMessage.error('获取房源详情失败')
    router.push('/houses')
  } finally {
    loading.value = false
  }
}

// 预约看房
const makeAppointment = () => {
  // TODO: 实现预约看房功能
  ElMessage.info('预约看房功能开发中')
}

// 收藏房源
const toggleFavorite = () => {
  // TODO: 实现收藏功能
  ElMessage.info('收藏功能开发中')
}

// 联系房东
const contactLandlord = () => {
  // TODO: 实现联系房东功能
  ElMessage.info('联系房东功能开发中')
}

// 图片切换
const changeImage = (index: number) => {
  currentImageIndex.value = index
}

onMounted(() => {
  fetchHouseDetail()
})
</script>

<template>
  <div class="house-detail-container" v-loading="loading">
    <div v-if="house" class="house-detail">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="router.go(-1)" icon="ArrowLeft">返回</el-button>
      </div>

      <!-- 房源图片 -->
      <div class="house-images">
        <div class="main-image">
          <el-image
            :src="house.images && house.images.length > 0 ? house.images[currentImageIndex].url : house.main_image_url || 'https://via.placeholder.com/800x400'"
            :alt="house.title"
            fit="cover"
            style="width: 100%; height: 400px"
          />
        </div>

        <div class="image-thumbnails" v-if="house.images && house.images.length > 1">
          <div
            v-for="(image, index) in house.images"
            :key="image.id"
            class="thumbnail"
            :class="{ active: index === currentImageIndex }"
            @click="changeImage(index)"
          >
            <el-image
              :src="image.url"
              :alt="`房源图片${index + 1}`"
              fit="cover"
              style="width: 100%; height: 80px"
            />
          </div>
        </div>
      </div>

      <!-- 房源信息 -->
      <div class="house-info">
        <el-row :gutter="20">
          <el-col :span="16">
            <div class="house-main-info">
              <h1 class="house-title">{{ house.title }}</h1>

              <div class="house-price">
                <span class="price">¥{{ house.price_monthly }}</span>
                <span class="unit">/月</span>
              </div>

              <div class="house-basic-info">
                <el-tag>{{ house.bedrooms }}室{{ house.bathrooms }}卫</el-tag>
                <el-tag>{{ house.area_sqm }}㎡</el-tag>
                <el-tag>{{ house.floor }}楼</el-tag>
                <el-tag type="success">{{ house.status }}</el-tag>
              </div>

              <div class="house-address">
                <el-icon><LocationFilled /></el-icon>
                {{ house.address }}
              </div>

              <div class="house-description">
                <h3>房源描述</h3>
                <p>{{ house.description }}</p>
              </div>

              <div class="house-features" v-if="house.features && house.features.length > 0">
                <h3>房源特色</h3>
                <div class="features-list">
                  <el-tag
                    v-for="feature in house.features"
                    :key="feature"
                    type="info"
                    class="feature-tag"
                  >
                    {{ feature }}
                  </el-tag>
                </div>
              </div>

              <div class="house-facilities" v-if="house.facilities && house.facilities.length > 0">
                <h3>配套设施</h3>
                <div class="facilities-list">
                  <el-tag
                    v-for="facility in house.facilities"
                    :key="facility"
                    type="warning"
                    class="facility-tag"
                  >
                    {{ facility }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="house-sidebar">
              <!-- 房东信息 -->
              <el-card class="landlord-card">
                <template #header>
                  <span>房东信息</span>
                </template>

                <div class="landlord-info">
                  <div class="landlord-avatar">
                    <el-avatar :size="60" icon="UserFilled" />
                  </div>
                  <div class="landlord-details">
                    <div class="landlord-name">{{ house.landlord.full_name }}</div>
                    <div class="landlord-phone">{{ house.landlord.phone_number }}</div>
                  </div>
                </div>

                <div class="contact-buttons">
                  <el-button type="primary" @click="contactLandlord" block>
                    <el-icon><ChatDotRound /></el-icon>
                    联系房东
                  </el-button>
                </div>
              </el-card>

              <!-- 操作按钮 -->
              <el-card class="action-card">
                <div class="action-buttons">
                  <el-button type="success" size="large" @click="makeAppointment" block>
                    <el-icon><Calendar /></el-icon>
                    预约看房
                  </el-button>

                  <el-button type="warning" size="large" @click="toggleFavorite" block>
                    <el-icon><Star /></el-icon>
                    收藏房源
                  </el-button>
                </div>
              </el-card>

              <!-- 房源统计 -->
              <el-card class="stats-card">
                <template #header>
                  <span>房源统计</span>
                </template>

                <div class="stats-list">
                  <div class="stat-item">
                    <span class="stat-label">发布时间</span>
                    <span class="stat-value">{{ new Date(house.created_at).toLocaleDateString() }}</span>
                  </div>

                  <div class="stat-item">
                    <span class="stat-label">浏览次数</span>
                    <span class="stat-value">{{ house.view_count || 0 }}</span>
                  </div>

                  <div class="stat-item">
                    <span class="stat-label">收藏次数</span>
                    <span class="stat-value">{{ house.favorite_count || 0 }}</span>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <el-empty v-else-if="!loading" description="房源不存在" />
  </div>
</template>

<style scoped>
.house-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.back-button {
  margin-bottom: 20px;
}

.house-images {
  margin-bottom: 30px;
}

.main-image {
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 15px;
}

.image-thumbnails {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 5px 0;
}

.thumbnail {
  flex-shrink: 0;
  width: 100px;
  height: 80px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s;
}

.thumbnail.active {
  border-color: var(--el-color-primary);
}

.thumbnail:hover {
  border-color: var(--el-color-primary-light-3);
}

.house-title {
  margin: 0 0 15px 0;
  font-size: 28px;
  color: #303133;
  font-weight: 600;
}

.house-price {
  margin-bottom: 15px;
}

.price {
  font-size: 32px;
  color: #f56c6c;
  font-weight: bold;
}

.unit {
  font-size: 16px;
  color: #909399;
  margin-left: 5px;
}

.house-basic-info {
  margin-bottom: 15px;
}

.house-basic-info .el-tag {
  margin-right: 10px;
}

.house-address {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  font-size: 16px;
  color: #606266;
}

.house-address .el-icon {
  margin-right: 8px;
  color: #909399;
}

.house-description,
.house-features,
.house-facilities {
  margin-bottom: 30px;
}

.house-description h3,
.house-features h3,
.house-facilities h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #303133;
}

.house-description p {
  line-height: 1.6;
  color: #606266;
  margin: 0;
}

.features-list,
.facilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag,
.facility-tag {
  margin: 0;
}

.house-sidebar .el-card {
  margin-bottom: 20px;
}

.landlord-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.landlord-avatar {
  margin-right: 15px;
}

.landlord-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.landlord-phone {
  font-size: 14px;
  color: #909399;
}

.action-buttons .el-button {
  margin-bottom: 15px;
}

.action-buttons .el-button:last-child {
  margin-bottom: 0;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #909399;
}

.stat-value {
  color: #303133;
  font-weight: bold;
}

@media (max-width: 768px) {
  .house-detail-container {
    padding: 10px;
  }

  .house-title {
    font-size: 24px;
  }

  .price {
    font-size: 28px;
  }

  .image-thumbnails {
    gap: 5px;
  }

  .thumbnail {
    width: 80px;
    height: 60px;
  }
}
</style>
