<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import leaseService from '@/services/lease.service'
import houseService from '@/services/house.service'
import userService from '@/services/user.service'
import type { CreateLeaseRequest } from '@/services/lease.service'

const router = useRouter()

// 表单引用
const leaseFormRef = ref<FormInstance>()

// 表单数据
const leaseForm = reactive<CreateLeaseRequest>({
  house_id: 0,
  tenant_id: 0,
  start_date: '',
  end_date: '',
  rent_amount_monthly: 0,
  deposit_amount: 0,
  payment_day: 1,
  terms: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  house_id: [
    { required: true, message: '请选择房源', trigger: 'change' }
  ],
  tenant_id: [
    { required: true, message: '请选择租客', trigger: 'change' }
  ],
  start_date: [
    { required: true, message: '请选择租约开始日期', trigger: 'change' }
  ],
  end_date: [
    { required: true, message: '请选择租约结束日期', trigger: 'change' }
  ],
  rent_amount_monthly: [
    { required: true, message: '请输入月租金', trigger: 'blur' },
    { type: 'number', min: 1, message: '月租金必须大于0', trigger: 'blur' }
  ],
  deposit_amount: [
    { required: true, message: '请输入押金', trigger: 'blur' },
    { type: 'number', min: 0, message: '押金不能小于0', trigger: 'blur' }
  ],
  payment_day: [
    { required: true, message: '请选择付款日', trigger: 'change' }
  ],
  terms: [
    { required: true, message: '请输入租约条款', trigger: 'blur' },
    { min: 10, max: 2000, message: '租约条款长度应为10-2000个字符', trigger: 'blur' }
  ]
})

// 房源选项
const houseOptions = ref<{ value: number; label: string; price: number; deposit: number }[]>([])

// 租客选项
const tenantOptions = ref<{ value: number; label: string; username: string }[]>([])

// 付款日选项
const paymentDayOptions = Array.from({ length: 28 }, (_, i) => ({
  value: i + 1,
  label: `每月${i + 1}日`
}))

const loading = ref(false)

// 获取可用房源
const fetchAvailableHouses = async () => {
  try {
    const response = await houseService.getHouseList({ status: '可租' })
    houseOptions.value = response.items.map(house => ({
      value: house.id,
      label: `${house.title} - ${house.address}`,
      price: house.price_monthly,
      deposit: house.deposit_amount
    }))
  } catch (error) {
    console.error('Failed to fetch houses:', error)
    ElMessage.error('获取房源列表失败')
  }
}

// 获取租客列表
const fetchTenants = async () => {
  try {
    const response = await userService.getUserList({ role: 'tenant' })
    tenantOptions.value = response.items.map(user => ({
      value: user.id,
      label: user.full_name,
      username: user.username
    }))
  } catch (error) {
    console.error('Failed to fetch tenants:', error)
    ElMessage.error('获取租客列表失败')
  }
}

// 处理房源选择变化
const handleHouseChange = (houseId: number) => {
  const selectedHouse = houseOptions.value.find(house => house.value === houseId)
  if (selectedHouse) {
    leaseForm.rent_amount_monthly = selectedHouse.price
    leaseForm.deposit_amount = selectedHouse.deposit
  }
}

// 处理日期变化
const handleStartDateChange = (date: string) => {
  if (date && !leaseForm.end_date) {
    // 默认设置为一年后
    const startDate = new Date(date)
    const endDate = new Date(startDate)
    endDate.setFullYear(endDate.getFullYear() + 1)
    leaseForm.end_date = endDate.toISOString().split('T')[0]
  }
}

// 验证结束日期
const validateEndDate = (rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请选择租约结束日期'))
  } else if (leaseForm.start_date && new Date(value) <= new Date(leaseForm.start_date)) {
    callback(new Error('结束日期必须晚于开始日期'))
  } else {
    callback()
  }
}

// 添加结束日期验证规则
rules.end_date.push({ validator: validateEndDate, trigger: 'change' })

// 提交表单
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  
  await formEl.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        
        await leaseService.createLease(leaseForm)
        
        ElMessage.success('租约创建成功')
        
        // 跳转到租约列表页
        router.push('/landlord/leases')
      } catch (error) {
        console.error('Failed to create lease:', error)
        ElMessage.error('创建租约失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 取消
const cancel = () => {
  router.push('/landlord/leases')
}

onMounted(() => {
  fetchAvailableHouses()
  fetchTenants()
})
</script>

<template>
  <div class="lease-create-container">
    <div class="page-header">
      <h1>创建租约</h1>
    </div>
    
    <el-card class="form-card" v-loading="loading">
      <el-form
        ref="leaseFormRef"
        :model="leaseForm"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="选择房源" prop="house_id">
              <el-select 
                v-model="leaseForm.house_id" 
                placeholder="请选择房源" 
                filterable
                @change="handleHouseChange"
                style="width: 100%"
              >
                <el-option
                  v-for="house in houseOptions"
                  :key="house.value"
                  :label="house.label"
                  :value="house.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="选择租客" prop="tenant_id">
              <el-select 
                v-model="leaseForm.tenant_id" 
                placeholder="请选择租客" 
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="tenant in tenantOptions"
                  :key="tenant.value"
                  :label="`${tenant.label} (${tenant.username})`"
                  :value="tenant.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="租约开始日期" prop="start_date">
              <el-date-picker
                v-model="leaseForm.start_date"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                @change="handleStartDateChange"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="租约结束日期" prop="end_date">
              <el-date-picker
                v-model="leaseForm.end_date"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="date => leaseForm.start_date && date.getTime() <= new Date(leaseForm.start_date).getTime()"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="月租金(元)" prop="rent_amount_monthly">
              <el-input-number 
                v-model="leaseForm.rent_amount_monthly" 
                :min="1" 
                :step="100" 
                style="width: 100%" 
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="押金(元)" prop="deposit_amount">
              <el-input-number 
                v-model="leaseForm.deposit_amount" 
                :min="0" 
                :step="100" 
                style="width: 100%" 
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="每月付款日" prop="payment_day">
              <el-select v-model="leaseForm.payment_day" placeholder="选择付款日" style="width: 100%">
                <el-option
                  v-for="day in paymentDayOptions"
                  :key="day.value"
                  :label="day.label"
                  :value="day.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="租约条款" prop="terms">
              <el-input
                v-model="leaseForm.terms"
                type="textarea"
                :rows="6"
                placeholder="请输入租约的详细条款，包括双方的权利义务、违约责任等"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm(leaseFormRef)" :loading="loading">
            创建租约
          </el-button>
          <el-button @click="cancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
.lease-create-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.form-card {
  margin-bottom: 20px;
}
</style>
