<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 当前用户信息
const currentUser = computed(() => authStore.user)

// 消息列表
interface Message {
  id: number
  sender: {
    id: number
    username: string
    full_name: string
    avatar_url?: string
  }
  receiver: {
    id: number
    username: string
    full_name: string
    avatar_url?: string
  }
  content: string
  is_read: boolean
  created_at: string
}

// 联系人列表
interface Contact {
  id: number
  username: string
  full_name: string
  avatar_url?: string
  unread_count: number
  last_message?: {
    content: string
    created_at: string
  }
}

const messages = ref<Message[]>([])
const contacts = ref<Contact[]>([])
const loading = ref(true)
const messageLoading = ref(false)
const selectedContact = ref<Contact | null>(null)
const messageContent = ref('')

// 获取联系人列表
const fetchContacts = async () => {
  try {
    loading.value = true
    
    // 模拟API调用
    setTimeout(() => {
      // 模拟数据
      contacts.value = [
        {
          id: 3,
          username: 'landlord1',
          full_name: '张房东',
          avatar_url: 'https://via.placeholder.com/40',
          unread_count: 2,
          last_message: {
            content: '好的，我会安排维修人员上门',
            created_at: '2023-05-20T14:30:00'
          }
        },
        {
          id: 4,
          username: 'landlord2',
          full_name: '李房东',
          avatar_url: 'https://via.placeholder.com/40',
          unread_count: 0,
          last_message: {
            content: '您好，请问您对房子还满意吗？',
            created_at: '2023-05-15T09:45:00'
          }
        }
      ]
      
      loading.value = false
      
      // 如果有联系人，默认选择第一个
      if (contacts.value.length > 0) {
        selectContact(contacts.value[0])
      }
    }, 500)
    
    // 实际API调用应该类似于:
    // const response = await api.get('/messages/contacts')
    // contacts.value = response.data
  } catch (error) {
    console.error('Failed to fetch contacts:', error)
    ElMessage.error('获取联系人列表失败')
    loading.value = false
  }
}

// 获取与特定用户的对话
const fetchMessages = async (userId: number) => {
  try {
    messageLoading.value = true
    
    // 模拟API调用
    setTimeout(() => {
      // 模拟数据
      messages.value = [
        {
          id: 1,
          sender: {
            id: currentUser.value?.id || 0,
            username: currentUser.value?.username || '',
            full_name: '我',
            avatar_url: 'https://via.placeholder.com/40'
          },
          receiver: {
            id: userId,
            username: selectedContact.value?.username || '',
            full_name: selectedContact.value?.full_name || '',
            avatar_url: selectedContact.value?.avatar_url
          },
          content: '您好，我想咨询一下房屋的维修问题',
          is_read: true,
          created_at: '2023-05-20T14:15:00'
        },
        {
          id: 2,
          sender: {
            id: userId,
            username: selectedContact.value?.username || '',
            full_name: selectedContact.value?.full_name || '',
            avatar_url: selectedContact.value?.avatar_url
          },
          receiver: {
            id: currentUser.value?.id || 0,
            username: currentUser.value?.username || '',
            full_name: '我',
            avatar_url: 'https://via.placeholder.com/40'
          },
          content: '您好，请问有什么可以帮到您的？',
          is_read: true,
          created_at: '2023-05-20T14:20:00'
        },
        {
          id: 3,
          sender: {
            id: currentUser.value?.id || 0,
            username: currentUser.value?.username || '',
            full_name: '我',
            avatar_url: 'https://via.placeholder.com/40'
          },
          receiver: {
            id: userId,
            username: selectedContact.value?.username || '',
            full_name: selectedContact.value?.full_name || '',
            avatar_url: selectedContact.value?.avatar_url
          },
          content: '厨房的水龙头漏水了，能否安排维修？',
          is_read: true,
          created_at: '2023-05-20T14:25:00'
        },
        {
          id: 4,
          sender: {
            id: userId,
            username: selectedContact.value?.username || '',
            full_name: selectedContact.value?.full_name || '',
            avatar_url: selectedContact.value?.avatar_url
          },
          receiver: {
            id: currentUser.value?.id || 0,
            username: currentUser.value?.username || '',
            full_name: '我',
            avatar_url: 'https://via.placeholder.com/40'
          },
          content: '好的，我会安排维修人员上门',
          is_read: false,
          created_at: '2023-05-20T14:30:00'
        }
      ]
      
      messageLoading.value = false
      
      // 标记未读消息为已读
      markMessagesAsRead()
      
      // 滚动到底部
      scrollToBottom()
    }, 500)
    
    // 实际API调用应该类似于:
    // const response = await api.get(`/messages/conversation/${userId}`)
    // messages.value = response.data.items
  } catch (error) {
    console.error('Failed to fetch messages:', error)
    ElMessage.error('获取消息列表失败')
    messageLoading.value = false
  }
}

// 选择联系人
const selectContact = (contact: Contact) => {
  selectedContact.value = contact
  fetchMessages(contact.id)
  
  // 更新未读消息数
  if (contact.unread_count > 0) {
    contact.unread_count = 0
  }
}

// 发送消息
const sendMessage = async () => {
  if (!messageContent.value.trim() || !selectedContact.value) return
  
  try {
    // 模拟API调用
    // 实际应该调用: 
    // await api.post('/messages', {
    //   receiver_id: selectedContact.value.id,
    //   content: messageContent.value
    // })
    
    // 添加到消息列表
    messages.value.push({
      id: Date.now(),
      sender: {
        id: currentUser.value?.id || 0,
        username: currentUser.value?.username || '',
        full_name: '我',
        avatar_url: 'https://via.placeholder.com/40'
      },
      receiver: {
        id: selectedContact.value.id,
        username: selectedContact.value.username,
        full_name: selectedContact.value.full_name,
        avatar_url: selectedContact.value.avatar_url
      },
      content: messageContent.value,
      is_read: false,
      created_at: new Date().toISOString()
    })
    
    // 更新联系人最后一条消息
    if (selectedContact.value) {
      selectedContact.value.last_message = {
        content: messageContent.value,
        created_at: new Date().toISOString()
      }
    }
    
    // 清空输入框
    messageContent.value = ''
    
    // 滚动到底部
    scrollToBottom()
  } catch (error) {
    console.error('Failed to send message:', error)
    ElMessage.error('发送消息失败')
  }
}

// 标记消息为已读
const markMessagesAsRead = async () => {
  // 实际应该调用API标记消息为已读
  // 这里只是模拟
  messages.value.forEach(message => {
    if (!message.is_read && message.sender.id === selectedContact.value?.id) {
      message.is_read = true
    }
  })
}

// 滚动到底部
const scrollToBottom = () => {
  setTimeout(() => {
    const messageContainer = document.querySelector('.message-container')
    if (messageContainer) {
      messageContainer.scrollTop = messageContainer.scrollHeight
    }
  }, 100)
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string) => {
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

onMounted(() => {
  fetchContacts()
})
</script>

<template>
  <div class="message-list-container">
    <div class="page-header">
      <h1>我的消息</h1>
    </div>
    
    <el-card class="message-card">
      <div class="message-layout">
        <!-- 联系人列表 -->
        <div class="contact-list" v-loading="loading">
          <div class="contact-header">
            <h3>联系人</h3>
          </div>
          
          <el-empty v-if="contacts.length === 0 && !loading" description="暂无联系人" />
          
          <div 
            v-for="contact in contacts" 
            :key="contact.id" 
            class="contact-item"
            :class="{ active: selectedContact?.id === contact.id }"
            @click="selectContact(contact)"
          >
            <el-avatar :src="contact.avatar_url || 'https://via.placeholder.com/40'" :size="40" />
            
            <div class="contact-info">
              <div class="contact-name">{{ contact.full_name }}</div>
              <div class="contact-last-message" v-if="contact.last_message">
                {{ contact.last_message.content }}
              </div>
            </div>
            
            <div class="contact-meta">
              <div class="contact-time" v-if="contact.last_message">
                {{ new Date(contact.last_message.created_at).toLocaleDateString() }}
              </div>
              <el-badge v-if="contact.unread_count > 0" :value="contact.unread_count" class="unread-badge" />
            </div>
          </div>
        </div>
        
        <!-- 消息区域 -->
        <div class="message-area">
          <template v-if="selectedContact">
            <!-- 消息头部 -->
            <div class="message-header">
              <h3>{{ selectedContact.full_name }}</h3>
            </div>
            
            <!-- 消息内容 -->
            <div class="message-container" v-loading="messageLoading">
              <div v-for="message in messages" :key="message.id" class="message-item" :class="{ 'message-self': message.sender.id === currentUser?.id }">
                <div class="message-avatar">
                  <el-avatar :src="message.sender.avatar_url || 'https://via.placeholder.com/40'" :size="40" />
                </div>
                
                <div class="message-bubble">
                  <div class="message-content">{{ message.content }}</div>
                  <div class="message-time">{{ formatDateTime(message.created_at) }}</div>
                </div>
              </div>
            </div>
            
            <!-- 消息输入框 -->
            <div class="message-input">
              <el-input
                v-model="messageContent"
                type="textarea"
                :rows="3"
                placeholder="请输入消息内容..."
                @keyup.enter.ctrl="sendMessage"
              />
              <div class="input-actions">
                <span class="input-tip">按 Ctrl + Enter 发送</span>
                <el-button type="primary" @click="sendMessage">发送</el-button>
              </div>
            </div>
          </template>
          
          <el-empty v-else description="请选择联系人开始聊天" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.message-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.message-card {
  min-height: 600px;
}

.message-layout {
  display: flex;
  height: 600px;
}

.contact-list {
  width: 300px;
  border-right: 1px solid #e6e6e6;
  overflow-y: auto;
}

.contact-header {
  padding: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.contact-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.contact-item:hover, .contact-item.active {
  background-color: #f5f7fa;
}

.contact-info {
  flex: 1;
  margin: 0 10px;
  overflow: hidden;
}

.contact-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #303133;
}

.contact-last-message {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.contact-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.unread-badge {
  margin-top: 5px;
}

.message-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.message-header {
  padding: 15px;
  border-bottom: 1px solid #e6e6e6;
}

.message-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.message-container {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.message-item {
  display: flex;
  margin-bottom: 15px;
}

.message-self {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 10px;
}

.message-bubble {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 10px;
  background-color: #f5f7fa;
  position: relative;
}

.message-self .message-bubble {
  background-color: #ecf5ff;
}

.message-content {
  word-break: break-word;
  color: #303133;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: right;
}

.message-input {
  padding: 15px;
  border-top: 1px solid #e6e6e6;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.input-tip {
  font-size: 12px;
  color: #909399;
}
</style>
