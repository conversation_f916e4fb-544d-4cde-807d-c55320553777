<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import MainLayout from '@/components/layout/MainLayout.vue'
import houseService from '@/services/house.service'
import type { HouseDetail } from '@/services/house.service'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 房源ID
const houseId = ref<number>(parseInt(route.params.id as string))

// 房源详情
const house = ref<HouseDetail | null>(null)
const loading = ref(true)

// 当前显示的图片索引
const activeImageIndex = ref(0)

// 预约看房表单
const appointmentForm = ref({
  date: '',
  time: '',
  message: ''
})

// 预约看房对话框
const appointmentDialogVisible = ref(false)

// 获取房源详情
const fetchHouseDetail = async () => {
  try {
    loading.value = true
    const response = await houseService.getHouseDetail(houseId.value)
    house.value = response
    
    // 记录浏览（实际应用中应调用API）
    console.log('记录房源浏览:', houseId.value)
  } catch (error) {
    console.error('Failed to fetch house detail:', error)
    ElMessage.error('获取房源详情失败')
  } finally {
    loading.value = false
  }
}

// 预览图片
const previewImages = () => {
  if (!house.value || house.value.images.length === 0) return
  
  // 实际应用中应使用图片预览组件
  window.open(house.value.images[activeImageIndex.value].url, '_blank')
}

// 切换图片
const changeImage = (index: number) => {
  activeImageIndex.value = index
}

// 打开预约看房对话框
const openAppointmentDialog = () => {
  if (!authStore.isLoggedIn) {
    ElMessageBox.confirm('预约看房需要先登录，是否前往登录页面？', '提示', {
      confirmButtonText: '前往登录',
      cancelButtonText: '取消',
      type: 'info'
    }).then(() => {
      router.push({
        path: '/login',
        query: { redirect: route.fullPath }
      })
    }).catch(() => {})
    return
  }
  
  appointmentDialogVisible.value = true
}

// 提交预约看房
const submitAppointment = () => {
  // 实际应用中应调用API
  ElMessage.success('预约看房请求已提交，请等待房东确认')
  appointmentDialogVisible.value = false
}

// 收藏房源
const toggleFavorite = () => {
  if (!authStore.isLoggedIn) {
    ElMessageBox.confirm('收藏房源需要先登录，是否前往登录页面？', '提示', {
      confirmButtonText: '前往登录',
      cancelButtonText: '取消',
      type: 'info'
    }).then(() => {
      router.push({
        path: '/login',
        query: { redirect: route.fullPath }
      })
    }).catch(() => {})
    return
  }
  
  // 实际应用中应调用API
  ElMessage.success('房源已收藏')
}

// 联系房东
const contactLandlord = () => {
  if (!authStore.isLoggedIn) {
    ElMessageBox.confirm('联系房东需要先登录，是否前往登录页面？', '提示', {
      confirmButtonText: '前往登录',
      cancelButtonText: '取消',
      type: 'info'
    }).then(() => {
      router.push({
        path: '/login',
        query: { redirect: route.fullPath }
      })
    }).catch(() => {})
    return
  }
  
  // 实际应用中应跳转到消息页面或打开消息对话框
  ElMessage.info('联系房东功能开发中...')
}

onMounted(() => {
  fetchHouseDetail()
})
</script>

<template>
  <main-layout>
    <div class="house-detail-container" v-loading="loading">
      <template v-if="house">
        <!-- 房源标题 -->
        <div class="house-header">
          <h1>{{ house.title }}</h1>
          <div class="house-address">
            <el-icon><location /></el-icon>
            <span>{{ house.address }}, {{ house.district }}, {{ house.city }}</span>
          </div>
        </div>
        
        <!-- 房源图片 -->
        <div class="house-gallery">
          <div class="main-image" @click="previewImages">
            <img :src="house.images[activeImageIndex]?.url || 'https://via.placeholder.com/800x500'" :alt="house.title">
          </div>
          <div class="thumbnail-list">
            <div
              v-for="(image, index) in house.images"
              :key="image.id"
              class="thumbnail"
              :class="{ active: index === activeImageIndex }"
              @click="changeImage(index)"
            >
              <img :src="image.url" :alt="`${house.title} - 图片 ${index + 1}`">
            </div>
          </div>
        </div>
        
        <!-- 房源信息 -->
        <el-row :gutter="20" class="house-info-section">
          <el-col :xs="24" :md="16">
            <!-- 基本信息 -->
            <el-card class="info-card">
              <template #header>
                <div class="card-header">
                  <h2>基本信息</h2>
                </div>
              </template>
              
              <el-descriptions :column="3" border>
                <el-descriptions-item label="月租金">
                  <span class="price">¥{{ house.price_monthly }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="押金">
                  ¥{{ house.deposit_amount }}
                </el-descriptions-item>
                <el-descriptions-item label="状态">
                  <el-tag :type="house.status === '可租' ? 'success' : 'info'">
                    {{ house.status }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="户型">
                  {{ house.bedrooms }}室{{ house.bathrooms }}卫
                </el-descriptions-item>
                <el-descriptions-item label="面积">
                  {{ house.area_sqm }}㎡
                </el-descriptions-item>
                <el-descriptions-item label="楼层">
                  {{ house.floor }}/{{ house.total_floors }}层
                </el-descriptions-item>
                <el-descriptions-item label="电梯">
                  {{ house.has_elevator ? '有' : '无' }}
                </el-descriptions-item>
                <el-descriptions-item label="可入住日期">
                  {{ house.available_from }}
                </el-descriptions-item>
                <el-descriptions-item label="发布时间">
                  {{ house.created_at }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
            
            <!-- 房源描述 -->
            <el-card class="info-card">
              <template #header>
                <div class="card-header">
                  <h2>房源描述</h2>
                </div>
              </template>
              
              <div class="description">
                {{ house.description }}
              </div>
            </el-card>
            
            <!-- 配套设施 -->
            <el-card class="info-card">
              <template #header>
                <div class="card-header">
                  <h2>配套设施</h2>
                </div>
              </template>
              
              <div class="amenities">
                <el-tag
                  v-for="(amenity, index) in house.amenities"
                  :key="index"
                  class="amenity-tag"
                >
                  {{ amenity }}
                </el-tag>
              </div>
            </el-card>
          </el-col>
          
          <el-col :xs="24" :md="8">
            <!-- 房东信息 -->
            <el-card class="info-card">
              <template #header>
                <div class="card-header">
                  <h2>房东信息</h2>
                </div>
              </template>
              
              <div class="landlord-info">
                <div class="landlord-name">
                  <h3>{{ house.landlord.full_name }}</h3>
                  <p>{{ house.landlord.username }}</p>
                </div>
                <div class="landlord-contact">
                  <p>
                    <el-icon><phone /></el-icon>
                    {{ house.landlord.phone_number }}
                  </p>
                </div>
                <div class="landlord-actions">
                  <el-button type="primary" @click="contactLandlord">
                    <el-icon><chat-dot-round /></el-icon>
                    联系房东
                  </el-button>
                </div>
              </div>
            </el-card>
            
            <!-- 操作按钮 -->
            <el-card class="info-card">
              <div class="action-buttons">
                <el-button type="primary" @click="openAppointmentDialog">
                  <el-icon><calendar /></el-icon>
                  预约看房
                </el-button>
                <el-button @click="toggleFavorite">
                  <el-icon><star /></el-icon>
                  收藏房源
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
        
        <!-- 预约看房对话框 -->
        <el-dialog
          v-model="appointmentDialogVisible"
          title="预约看房"
          width="500px"
        >
          <el-form :model="appointmentForm" label-width="100px">
            <el-form-item label="预约日期">
              <el-date-picker
                v-model="appointmentForm.date"
                type="date"
                placeholder="选择日期"
                :disabled-date="date => date.getTime() < Date.now() - 8.64e7"
              />
            </el-form-item>
            <el-form-item label="预约时间">
              <el-time-picker
                v-model="appointmentForm.time"
                placeholder="选择时间"
                format="HH:mm"
              />
            </el-form-item>
            <el-form-item label="留言">
              <el-input
                v-model="appointmentForm.message"
                type="textarea"
                rows="3"
                placeholder="请输入您的看房需求或问题"
              />
            </el-form-item>
          </el-form>
          <template #footer>
            <span class="dialog-footer">
              <el-button @click="appointmentDialogVisible = false">取消</el-button>
              <el-button type="primary" @click="submitAppointment">提交预约</el-button>
            </span>
          </template>
        </el-dialog>
      </template>
      
      <el-empty v-else-if="!loading" description="房源不存在或已下架" />
    </div>
  </main-layout>
</template>

<style scoped>
.house-detail-container {
  padding: 20px;
}

.house-header {
  margin-bottom: 20px;
}

.house-header h1 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #303133;
}

.house-address {
  display: flex;
  align-items: center;
  color: #909399;
}

.house-address .el-icon {
  margin-right: 5px;
}

.house-gallery {
  margin-bottom: 20px;
}

.main-image {
  height: 400px;
  overflow: hidden;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 10px;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-list {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.thumbnail.active {
  opacity: 1;
  border: 2px solid var(--el-color-primary);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-info-section {
  margin-bottom: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.price {
  color: #f56c6c;
  font-weight: bold;
  font-size: 18px;
}

.description {
  line-height: 1.6;
  color: #606266;
  white-space: pre-line;
}

.amenities {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.amenity-tag {
  margin-right: 0;
}

.landlord-info {
  padding: 10px 0;
}

.landlord-name h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.landlord-name p {
  margin: 0 0 10px 0;
  color: #909399;
}

.landlord-contact p {
  display: flex;
  align-items: center;
  margin: 5px 0;
}

.landlord-contact .el-icon {
  margin-right: 5px;
}

.landlord-actions {
  margin-top: 15px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-buttons .el-button {
  width: 100%;
}
</style>
