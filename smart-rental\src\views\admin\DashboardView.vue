<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import userService from '@/services/user.service'
import houseService from '@/services/house.service'
import leaseService from '@/services/lease.service'

const router = useRouter()
const authStore = useAuthStore()

// 用户信息
const user = ref(authStore.user)

// 统计数据
const stats = ref({
  userCount: 0,
  houseCount: 0,
  leaseCount: 0,
  tenantCount: 0,
  landlordCount: 0,
  monthlyRevenue: 0
})

// 最近用户
const recentUsers = ref([])

// 最近房源
const recentHouses = ref([])

// 获取统计数据
const fetchStats = async () => {
  try {
    // 获取用户统计
    const userResponse = await userService.getUserList()
    stats.value.userCount = userResponse.length

    // 获取房源统计
    const houseResponse = await houseService.getHouseList()
    stats.value.houseCount = houseResponse.length

    // 获取租约统计
    const leaseResponse = await leaseService.getLeaseList()
    stats.value.leaseCount = leaseResponse.length

    // 获取租客数量
    const tenantResponse = await userService.getUserList({ role: 'tenant' })
    stats.value.tenantCount = tenantResponse.length

    // 获取房东数量
    const landlordResponse = await userService.getUserList({ role: 'landlord' })
    stats.value.landlordCount = landlordResponse.length

    // 计算月收入（这里简化处理）
    const activeLeases = await leaseService.getLeaseList({ status: '生效中' })
    stats.value.monthlyRevenue = activeLeases.reduce((sum, lease) => sum + (lease.monthly_rent || 0), 0) * 0.05 // 假设平台抽成5%
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

// 获取最近用户
const fetchRecentUsers = async () => {
  try {
    const response = await userService.getUserList()
    recentUsers.value = response.slice(0, 5) // 取前5个
  } catch (error) {
    console.error('Failed to fetch recent users:', error)
  }
}

// 获取最近房源
const fetchRecentHouses = async () => {
  try {
    const response = await houseService.getHouseList()
    recentHouses.value = response.slice(0, 5) // 取前5个
  } catch (error) {
    console.error('Failed to fetch recent houses:', error)
  }
}

// 跳转到详情页
const goToDetail = (path: string) => {
  router.push(path)
}

onMounted(() => {
  fetchStats()
  fetchRecentUsers()
  fetchRecentHouses()
})
</script>

<template>
  <div class="dashboard-container">
    <!-- 欢迎信息 -->
    <div class="welcome-section">
      <h1>欢迎回来，{{ user?.username }}</h1>
      <p>这是系统管理控制台，您可以在这里管理整个平台的运营。</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stat-cards">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><user /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.userCount }}</div>
              <div class="stat-label">总用户数</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/admin/users')">查看详情</el-button>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><house /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.houseCount }}</div>
              <div class="stat-label">房源总数</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/admin/houses')">查看详情</el-button>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><document /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.leaseCount }}</div>
              <div class="stat-label">租约总数</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/admin/leases')">查看详情</el-button>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon"><money /></el-icon>
            <div class="stat-info">
              <div class="stat-value">¥{{ stats.monthlyRevenue }}</div>
              <div class="stat-label">月收入</div>
            </div>
          </div>
          <el-button text @click="goToDetail('/admin/reports')">查看详情</el-button>
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户分布 -->
    <el-row :gutter="20" class="user-stats">
      <el-col :span="12">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon tenant-icon"><user /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.tenantCount }}</div>
              <div class="stat-label">租客数量</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon class="stat-icon landlord-icon"><user /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ stats.landlordCount }}</div>
              <div class="stat-label">房东数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近用户 -->
    <el-card class="recent-section">
      <template #header>
        <div class="card-header">
          <h2>最近注册用户</h2>
          <el-button text @click="goToDetail('/admin/users')">查看全部</el-button>
        </div>
      </template>

      <el-empty v-if="recentUsers.length === 0" description="暂无用户记录" />

      <el-table v-else :data="recentUsers" style="width: 100%">
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="full_name" label="姓名" width="150" />
        <el-table-column prop="email" label="邮箱" width="200" />
        <el-table-column prop="role" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="row.role === 'landlord' ? 'warning' : row.role === 'tenant' ? 'success' : 'info'">
              {{ row.role === 'landlord' ? '房东' : row.role === 'tenant' ? '租客' : '管理员' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="registration_date" label="注册时间" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              text
              @click="goToDetail(`/admin/users/${row.id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 最近房源 -->
    <el-card class="recent-section">
      <template #header>
        <div class="card-header">
          <h2>最近发布房源</h2>
          <el-button text @click="goToDetail('/admin/houses')">查看全部</el-button>
        </div>
      </template>

      <el-empty v-if="recentHouses.length === 0" description="暂无房源记录" />

      <el-table v-else :data="recentHouses" style="width: 100%">
        <el-table-column label="房源信息" width="250">
          <template #default="{ row }">
            <div class="house-info">
              <el-image
                :src="row.main_image_url"
                :alt="row.title"
                class="house-image"
                fit="cover"
              />
              <div>
                <div class="house-title">{{ row.title }}</div>
                <div class="house-address">{{ row.address }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="房东" width="150">
          <template #default="{ row }">
            {{ row.landlord.full_name }}
          </template>
        </el-table-column>

        <el-table-column prop="price_monthly" label="月租金" width="120">
          <template #default="{ row }">
            ¥{{ row.price_monthly }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="row.status === '可租' ? 'success' : row.status === '已租' ? 'info' : 'warning'">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="发布时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.created_at).toLocaleDateString() }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              text
              @click="goToDetail(`/houses/${row.id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-section h1 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #303133;
}

.welcome-section p {
  margin: 0;
  color: #606266;
}

.stat-cards, .user-stats {
  margin-bottom: 30px;
}

.stat-card {
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.stat-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-right: 15px;
}

.tenant-icon {
  color: #67c23a;
}

.landlord-icon {
  color: #e6a23c;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  color: #909399;
}

.recent-section {
  margin-bottom: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-image {
  width: 60px;
  height: 40px;
  margin-right: 10px;
  border-radius: 4px;
}

.house-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.house-address {
  font-size: 12px;
  color: #909399;
}
</style>
