<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import repairService from '@/services/repair.service'
import type { Repair } from '@/services/repair.service'

const router = useRouter()
const repairs = ref<Repair[]>([])
const loading = ref(true)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 筛选条件
const filters = reactive({
  status: '',
  page: 1,
  per_page: 10
})

// 状态选项
const statusOptions = [
  { value: '', label: '全部' },
  { value: '待处理', label: '待处理' },
  { value: '处理中', label: '处理中' },
  { value: '已完成', label: '已完成' }
]

// 紧急程度映射
const urgencyMap = {
  '紧急': 'danger',
  '一般': 'warning',
  '低': 'info'
}

// 获取维修请求列表
const fetchRepairs = async () => {
  try {
    loading.value = true

    const response = await repairService.getRepairList({
      status: filters.status,
      page: filters.page,
      per_page: filters.per_page
    })

    repairs.value = response.items
    total.value = response.total
  } catch (error) {
    console.error('Failed to fetch repairs:', error)
    ElMessage.error('获取维修请求列表失败')
  } finally {
    loading.value = false
  }
}

// 创建新的维修请求
const createRepair = () => {
  router.push('/tenant/repairs/create')
}

// 查看维修请求详情
const viewRepairDetail = (repairId: number) => {
  router.push(`/tenant/repairs/${repairId}`)
}

// 处理筛选变化
const handleFilterChange = () => {
  filters.page = 1
  fetchRepairs()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchRepairs()
}

// 格式化日期时间
const formatDateTime = (dateTimeStr: string | null) => {
  if (!dateTimeStr) return '暂无'
  const date = new Date(dateTimeStr)
  return date.toLocaleString()
}

onMounted(() => {
  fetchRepairs()
})
</script>

<template>
  <div class="repair-list-container">
    <div class="page-header">
      <h1>维修请求</h1>
      <el-button type="primary" @click="createRepair">新建维修请求</el-button>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable @change="handleFilterChange">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </el-card>

    <div class="repair-list" v-loading="loading">
      <el-empty v-if="repairs.length === 0 && !loading" description="暂无维修请求记录" />

      <el-card v-for="repair in repairs" :key="repair.id" class="repair-item">
        <div class="repair-header">
          <div class="repair-title">
            <h3>{{ repair.house.title }}</h3>
            <el-tag
              :type="
                repair.status === '已完成' ? 'success' :
                repair.status === '处理中' ? 'warning' : 'info'
              "
            >
              {{ repair.status }}
            </el-tag>
            <el-tag :type="urgencyMap[repair.urgency_level as keyof typeof urgencyMap]">
              {{ repair.urgency_level }}
            </el-tag>
          </div>
          <div class="repair-date">
            提交时间: {{ formatDateTime(repair.submitted_at) }}
          </div>
        </div>

        <div class="repair-content">
          <div class="repair-description">
            <div class="label">问题描述:</div>
            <div class="value">{{ repair.description }}</div>
          </div>

          <div class="repair-response" v-if="repair.landlord_response">
            <div class="label">房东回复:</div>
            <div class="value">{{ repair.landlord_response }}</div>
          </div>

          <div class="repair-completion" v-if="repair.completed_at">
            <div class="label">完成时间:</div>
            <div class="value">{{ formatDateTime(repair.completed_at) }}</div>
          </div>

          <div class="repair-address">
            <div class="label">房屋地址:</div>
            <div class="value">{{ repair.house.address }}</div>
          </div>
        </div>

        <div class="repair-actions">
          <el-button type="primary" @click="viewRepairDetail(repair.id)">查看详情</el-button>
        </div>
      </el-card>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.repair-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.repair-list {
  min-height: 400px;
}

.repair-item {
  margin-bottom: 20px;
}

.repair-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.repair-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.repair-title h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.repair-date {
  color: #909399;
  font-size: 14px;
}

.repair-content {
  margin-bottom: 15px;
}

.repair-description, .repair-response, .repair-completion, .repair-address {
  margin-bottom: 10px;
}

.label {
  color: #606266;
  font-weight: bold;
  margin-bottom: 5px;
}

.value {
  color: #303133;
}

.repair-actions {
  display: flex;
  justify-content: flex-end;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
