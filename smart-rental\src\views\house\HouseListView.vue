<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import MainLayout from '@/components/layout/MainLayout.vue'
import houseService from '@/services/house.service'
import type { HouseListItem } from '@/services/house.service'

const router = useRouter()

// 房源列表数据
const houses = ref<HouseListItem[]>([])
const loading = ref(true)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 筛选条件
const filters = reactive({
  city: '',
  district: '',
  min_price: undefined as number | undefined,
  max_price: undefined as number | undefined,
  bedrooms: undefined as number | undefined,
  status: '',
  page: 1,
  per_page: 12
})

// 城市和区域选项（实际应用中应从API获取）
const cityOptions = [
  { value: '北京', label: '北京' },
  { value: '上海', label: '上海' },
  { value: '广州', label: '广州' },
  { value: '深圳', label: '深圳' }
]

const districtOptions = ref<{ value: string; label: string }[]>([])

// 监听城市变化，更新区域选项
watch(() => filters.city, (newCity) => {
  filters.district = ''

  if (newCity === '北京') {
    districtOptions.value = [
      { value: '朝阳区', label: '朝阳区' },
      { value: '海淀区', label: '海淀区' },
      { value: '东城区', label: '东城区' },
      { value: '西城区', label: '西城区' }
    ]
  } else if (newCity === '上海') {
    districtOptions.value = [
      { value: '浦东新区', label: '浦东新区' },
      { value: '徐汇区', label: '徐汇区' },
      { value: '静安区', label: '静安区' },
      { value: '黄浦区', label: '黄浦区' }
    ]
  } else {
    districtOptions.value = []
  }
})

// 获取房源列表
const fetchHouses = async () => {
  try {
    loading.value = true

    // 构建查询参数，过滤掉undefined值
    const params = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)

    const response = await houseService.getHouseList(params)
    houses.value = response // 直接使用数组响应
    total.value = response.length // 使用数组长度作为总数
  } catch (error) {
    console.error('Failed to fetch houses:', error)
    houses.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    city: '',
    district: '',
    min_price: undefined,
    max_price: undefined,
    bedrooms: undefined,
    status: '',
    page: 1,
    per_page: 12
  })

  fetchHouses()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchHouses()
}

// 跳转到房源详情页
const viewHouseDetail = (id: number) => {
  router.push(`/houses/${id}`)
}

onMounted(() => {
  fetchHouses()
})
</script>

<template>
  <main-layout>
    <div class="house-list-container">
      <!-- 筛选区域 -->
      <el-card class="filter-card">
        <el-form :model="filters" label-width="80px" inline>
          <el-form-item label="城市">
            <el-select v-model="filters.city" placeholder="选择城市" clearable>
              <el-option
                v-for="item in cityOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="区域">
            <el-select v-model="filters.district" placeholder="选择区域" clearable :disabled="!filters.city">
              <el-option
                v-for="item in districtOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="价格区间">
            <div class="price-range">
              <el-input-number v-model="filters.min_price" :min="0" :step="500" placeholder="最低价" />
              <span class="separator">-</span>
              <el-input-number v-model="filters.max_price" :min="0" :step="500" placeholder="最高价" />
            </div>
          </el-form-item>

          <el-form-item label="户型">
            <el-select v-model="filters.bedrooms" placeholder="选择户型" clearable>
              <el-option :value="1" label="1室" />
              <el-option :value="2" label="2室" />
              <el-option :value="3" label="3室" />
              <el-option :value="4" label="4室及以上" />
            </el-select>
          </el-form-item>

          <el-form-item label="状态">
            <el-select v-model="filters.status" placeholder="房源状态" clearable>
              <el-option value="可租" label="可租" />
              <el-option value="已租" label="已租" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="fetchHouses">搜索</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 房源列表 -->
      <div class="house-list" v-loading="loading">
        <el-empty v-if="houses.length === 0 && !loading" description="暂无符合条件的房源" />

        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="house in houses" :key="house.id">
            <el-card class="house-card" @click="viewHouseDetail(house.id)">
              <div class="house-image">
                <img :src="house.main_image_url || 'https://via.placeholder.com/300x200'" :alt="house.title">
                <div class="house-price">¥{{ house.price_monthly }}/月</div>
              </div>
              <div class="house-info">
                <h3 class="house-title">{{ house.title }}</h3>
                <p class="house-address">{{ house.address }}</p>
                <div class="house-tags">
                  <el-tag size="small">{{ house.bedrooms }}室{{ house.bathrooms }}卫</el-tag>
                  <el-tag size="small">{{ house.area_sqm }}㎡</el-tag>
                  <el-tag size="small" :type="house.status === '可租' ? 'success' : 'info'">{{ house.status }}</el-tag>
                </div>
                <div class="house-landlord">
                  <span>房东: {{ house.landlord.full_name }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 分页 -->
        <div class="pagination-container" v-if="total > 0">
          <el-pagination
            v-model:current-page="filters.page"
            :page-size="filters.per_page"
            :total="total"
            layout="total, prev, pager, next, jumper"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>
  </main-layout>
</template>

<style scoped>
.house-list-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.price-range {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 10px;
}

.house-list {
  min-height: 400px;
}

.house-card {
  margin-bottom: 20px;
  cursor: pointer;
  transition: transform 0.3s;
}

.house-card:hover {
  transform: translateY(-5px);
}

.house-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-price {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--el-color-primary);
  color: white;
  padding: 5px 10px;
  font-weight: bold;
}

.house-info {
  padding: 15px;
}

.house-title {
  margin: 0 0 10px 0;
  font-size: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-address {
  color: #909399;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-tags {
  display: flex;
  gap: 5px;
  margin-bottom: 10px;
}

.house-landlord {
  font-size: 12px;
  color: #909399;
}

.pagination-container {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}
</style>
