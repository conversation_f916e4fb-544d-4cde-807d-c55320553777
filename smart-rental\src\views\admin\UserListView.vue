<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import userService from '@/services/user.service'
import type { User } from '@/services/user.service'

// 用户列表数据
const users = ref<User[]>([])
const loading = ref(true)
const total = ref(0)

// 筛选条件
const filters = reactive({
  role: '',
  status: '',
  keyword: '',
  page: 1,
  per_page: 10
})

// 角色选项
const roleOptions = [
  { value: '', label: '全部角色' },
  { value: 'tenant', label: '租客' },
  { value: 'landlord', label: '房东' },
  { value: 'admin', label: '管理员' }
]

// 状态选项
const statusOptions = [
  { value: '', label: '全部状态' },
  { value: 'active', label: '正常' },
  { value: 'inactive', label: '禁用' },
  { value: 'pending', label: '待审核' }
]

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true

    const params = Object.entries(filters).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {} as Record<string, any>)

    const response = await userService.getUserList(params)
    users.value = response
    total.value = response.length
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 禁用用户
const disableUser = (userId: number) => {
  ElMessageBox.confirm('确定要禁用该用户吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.updateUserStatus(userId, 'inactive')

      // 更新状态
      const index = users.value.findIndex(item => item.id === userId)
      if (index !== -1) {
        users.value[index].status = 'inactive'
      }

      ElMessage.success('用户已禁用')
    } catch (error) {
      console.error('Failed to disable user:', error)
      ElMessage.error('禁用用户失败')
    }
  }).catch(() => {})
}

// 启用用户
const enableUser = async (userId: number) => {
  try {
    await userService.updateUserStatus(userId, 'active')

    // 更新状态
    const index = users.value.findIndex(item => item.id === userId)
    if (index !== -1) {
      users.value[index].status = 'active'
    }

    ElMessage.success('用户已启用')
  } catch (error) {
    console.error('Failed to enable user:', error)
    ElMessage.error('启用用户失败')
  }
}

// 删除用户
const deleteUser = (userId: number) => {
  ElMessageBox.confirm('确定要删除该用户吗？删除后无法恢复。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.deleteUser(userId)

      // 从列表中移除
      users.value = users.value.filter(item => item.id !== userId)
      total.value--

      ElMessage.success('用户已删除')
    } catch (error) {
      console.error('Failed to delete user:', error)
      ElMessage.error('删除用户失败')
    }
  }).catch(() => {})
}

// 重置密码
const resetPassword = (userId: number) => {
  ElMessageBox.confirm('确定要重置该用户的密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await userService.resetPassword(userId)

      ElMessageBox.alert(`新密码为：${response.new_password}`, '密码重置成功', {
        confirmButtonText: '确定',
        type: 'success'
      })
    } catch (error) {
      console.error('Failed to reset password:', error)
      ElMessage.error('重置密码失败')
    }
  }).catch(() => {})
}

// 处理搜索
const handleSearch = () => {
  filters.page = 1
  fetchUsers()
}

// 重置筛选条件
const resetFilters = () => {
  Object.assign(filters, {
    role: '',
    status: '',
    keyword: '',
    page: 1,
    per_page: 10
  })

  fetchUsers()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  filters.page = page
  fetchUsers()
}

// 格式化角色
const formatRole = (role: string) => {
  const roleMap: Record<string, string> = {
    'tenant': '租客',
    'landlord': '房东',
    'admin': '管理员'
  }
  return roleMap[role] || role
}

// 格式化状态
const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': 'success',
    'inactive': 'danger',
    'pending': 'warning'
  }
  return statusMap[status] || 'info'
}

const formatStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    'active': '正常',
    'inactive': '禁用',
    'pending': '待审核'
  }
  return statusMap[status] || status
}

onMounted(() => {
  fetchUsers()
})
</script>

<template>
  <div class="user-list-container">
    <div class="page-header">
      <h1>用户管理</h1>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card">
      <el-form :model="filters" inline>
        <el-form-item label="角色">
          <el-select v-model="filters.role" placeholder="选择角色" clearable>
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="选择状态" clearable>
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="关键词">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索用户名、姓名、邮箱"
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <div class="user-list" v-loading="loading">
      <el-empty v-if="users.length === 0 && !loading" description="暂无用户记录" />

      <el-table :data="users" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="username" label="用户名" width="150" />

        <el-table-column prop="full_name" label="姓名" width="120" />

        <el-table-column prop="email" label="邮箱" min-width="200" />

        <el-table-column prop="phone_number" label="手机号" width="150" />

        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="row.role === 'admin' ? 'danger' : row.role === 'landlord' ? 'warning' : 'success'">
              {{ formatRole(row.role) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ formatStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="registration_date" label="注册时间" width="180">
          <template #default="{ row }">
            {{ new Date(row.registration_date).toLocaleString() }}
          </template>
        </el-table-column>

        <el-table-column prop="last_login" label="最后登录" width="180">
          <template #default="{ row }">
            {{ row.last_login ? new Date(row.last_login).toLocaleString() : '从未登录' }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              v-if="row.status === 'inactive'"
              @click="enableUser(row.id)"
            >
              启用
            </el-button>
            <el-button
              type="warning"
              size="small"
              v-if="row.status === 'active'"
              @click="disableUser(row.id)"
            >
              禁用
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="resetPassword(row.id)"
            >
              重置密码
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteUser(row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="filters.page"
          :page-size="filters.per_page"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.user-list-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.user-list {
  min-height: 400px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
