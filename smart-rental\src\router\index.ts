import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'
import authService from '@/services/auth.service'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { title: '首页' }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: { title: '登录', guest: true }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('@/views/RegisterView.vue'),
      meta: { title: '注册', guest: true }
    },
    {
      path: '/houses',
      name: 'houses',
      component: () => import('@/views/house/HouseListView.vue'),
      meta: { title: '房源列表' }
    },
    {
      path: '/houses/:id',
      name: 'house-detail',
      component: () => import('@/views/HouseDetailView.vue'),
      meta: { title: '房源详情' }
    },
    {
      path: '/debug',
      name: 'debug',
      component: () => import('@/views/DebugView.vue'),
      meta: { title: '调试工具' }
    },
    // 租客路由
    {
      path: '/tenant',
      name: 'tenant',
      component: () => import('@/views/layouts/TenantLayout.vue'),
      meta: { requiresAuth: true, role: 'tenant' },
      children: [
        {
          path: 'dashboard',
          name: 'tenant-dashboard',
          component: () => import('@/views/tenant/DashboardView.vue'),
          meta: { title: '租客控制台' }
        },
        {
          path: 'appointments',
          name: 'tenant-appointments',
          component: () => import('@/views/tenant/AppointmentListView.vue'),
          meta: { title: '我的预约' }
        },
        {
          path: 'leases',
          name: 'tenant-leases',
          component: () => import('@/views/tenant/LeaseListView.vue'),
          meta: { title: '我的租约' }
        },
        {
          path: 'repairs',
          name: 'tenant-repairs',
          component: () => import('@/views/tenant/RepairListView.vue'),
          meta: { title: '维修请求' }
        },
        {
          path: 'repairs/create',
          name: 'tenant-repair-create',
          component: () => import('@/views/tenant/RepairCreateView.vue'),
          meta: { title: '新建维修请求' }
        },
        {
          path: 'repairs/:id',
          name: 'tenant-repair-detail',
          component: () => import('@/views/tenant/RepairDetailView.vue'),
          meta: { title: '维修请求详情' }
        },
        {
          path: 'favorites',
          name: 'tenant-favorites',
          component: () => import('@/views/tenant/FavoriteListView.vue'),
          meta: { title: '我的收藏' }
        },
        {
          path: 'messages',
          name: 'tenant-messages',
          component: () => import('@/views/tenant/MessageListView.vue'),
          meta: { title: '我的消息' }
        },
        {
          path: 'profile',
          name: 'tenant-profile',
          component: () => import('@/views/tenant/ProfileView.vue'),
          meta: { title: '个人资料' }
        }
      ]
    },
    // 房东路由
    {
      path: '/landlord',
      name: 'landlord',
      component: () => import('@/views/layouts/LandlordLayout.vue'),
      meta: { requiresAuth: true, role: 'landlord' },
      children: [
        {
          path: 'dashboard',
          name: 'landlord-dashboard',
          component: () => import('@/views/landlord/DashboardView.vue'),
          meta: { title: '房东控制台' }
        },
        {
          path: 'houses',
          name: 'landlord-houses',
          component: () => import('@/views/landlord/HouseListView.vue'),
          meta: { title: '我的房源' }
        },
        {
          path: 'houses/create',
          name: 'landlord-house-create',
          component: () => import('@/views/landlord/HouseCreateView.vue'),
          meta: { title: '发布房源' }
        },
        // {
        //   path: 'houses/:id/edit',
        //   name: 'landlord-house-edit',
        //   component: () => import('../views/landlord/HouseEditView.vue'),
        //   meta: { title: '编辑房源' }
        // },
        {
          path: 'appointments',
          name: 'landlord-appointments',
          component: () => import('@/views/landlord/AppointmentListView.vue'),
          meta: { title: '看房预约' }
        },
        {
          path: 'leases',
          name: 'landlord-leases',
          component: () => import('@/views/landlord/LeaseListView.vue'),
          meta: { title: '租约管理' }
        },
        // {
        //   path: 'leases/create',
        //   name: 'landlord-lease-create',
        //   component: () => import('../views/landlord/LeaseCreateView.vue'),
        //   meta: { title: '创建租约' }
        // },
        {
          path: 'repairs',
          name: 'landlord-repairs',
          component: () => import('@/views/landlord/RepairListView.vue'),
          meta: { title: '维修管理' }
        },
        {
          path: 'payments',
          name: 'landlord-payments',
          component: () => import('@/views/landlord/PaymentListView.vue'),
          meta: { title: '收款管理' }
        },
        {
          path: 'reports',
          name: 'landlord-reports',
          component: () => import('@/views/landlord/ReportView.vue'),
          meta: { title: '数据报表' }
        },
        {
          path: 'messages',
          name: 'landlord-messages',
          component: () => import('@/views/landlord/MessageListView.vue'),
          meta: { title: '我的消息' }
        },
        {
          path: 'profile',
          name: 'landlord-profile',
          component: () => import('@/views/landlord/ProfileView.vue'),
          meta: { title: '个人资料' }
        }
      ]
    },
    // 管理员路由
    {
      path: '/admin',
      name: 'admin',
      component: () => import('@/views/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true, role: 'admin' },
      children: [
        {
          path: 'dashboard',
          name: 'admin-dashboard',
          component: () => import('@/views/admin/DashboardView.vue'),
          meta: { title: '管理员控制台' }
        },
        {
          path: 'users',
          name: 'admin-users',
          component: () => import('@/views/admin/UserListView.vue'),
          meta: { title: '用户管理' }
        },
        {
          path: 'houses',
          name: 'admin-houses',
          component: () => import('@/views/admin/HouseListView.vue'),
          meta: { title: '房源管理' }
        },
        {
          path: 'leases',
          name: 'admin-leases',
          component: () => import('@/views/admin/LeaseListView.vue'),
          meta: { title: '租约管理' }
        },
        {
          path: 'reports',
          name: 'admin-reports',
          component: () => import('@/views/admin/ReportView.vue'),
          meta: { title: '系统报表' }
        }
      ]
    },
    // 404页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFoundView.vue'),
      meta: { title: '页面不存在' }
    }
  ]
})

// 全局前置守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 智能房屋租赁系统` : '智能房屋租赁系统'

  const isLoggedIn = authService.isLoggedIn()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const isGuestOnly = to.matched.some(record => record.meta.guest)

  // 获取当前用户角色
  const currentUser = authService.getCurrentUser()
  const userRole = currentUser?.role

  // 检查路由是否需要特定角色
  const requiredRole = to.matched.find(record => record.meta.role)?.meta.role

  if (requiresAuth && !isLoggedIn) {
    // 需要登录但未登录，重定向到登录页
    ElMessage.warning('请先登录')
    next({ name: 'login', query: { redirect: to.fullPath } })
  } else if (isGuestOnly && isLoggedIn) {
    // 仅限游客但已登录，重定向到首页
    next({ name: 'home' })
  } else if (requiresAuth && requiredRole && userRole !== requiredRole) {
    // 需要特定角色但角色不匹配，重定向到首页
    ElMessage.error('您没有权限访问该页面')
    next({ name: 'home' })
  } else {
    // 其他情况正常通过
    next()
  }
})

export default router
