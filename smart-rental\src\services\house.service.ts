import api from './api'
import type { PaginatedResponse } from './user.service'

export interface HouseListItem {
  id: number
  title: string
  address: string
  city: string
  district: string
  price_monthly: number
  bedrooms: number
  bathrooms: number
  area_sqm: number
  status: string
  main_image_url: string
  landlord: {
    id: number
    username: string
    full_name: string
  }
}

export interface HouseImage {
  id: number
  url: string
  is_main: boolean
}

export interface HouseDetail {
  id: number
  title: string
  description: string
  address: string
  city: string
  district: string
  price_monthly: number
  deposit_amount: number
  bedrooms: number
  bathrooms: number
  area_sqm: number
  floor: number
  total_floors: number
  has_elevator: boolean
  amenities: string[]
  status: string
  available_from: string
  images: HouseImage[]
  landlord: {
    id: number
    username: string
    full_name: string
    phone_number: string
  }
  created_at: string
  updated_at: string
}

export interface CreateHouseRequest {
  title: string
  description: string
  address: string
  city: string
  district: string
  price_monthly: number
  deposit_amount: number
  bedrooms: number
  bathrooms: number
  area_sqm: number
  floor: number
  total_floors: number
  has_elevator: boolean
  amenities: string[]
  available_from: string
}

export interface CreateHouseResponse {
  id: number
  title: string
  status: string
  message: string
}

export interface UpdateHouseResponse {
  id: number
  title: string
  message: string
}

export interface UploadImagesResponse {
  images: HouseImage[]
}

export interface MessageResponse {
  message: string
}

class HouseService {
  /**
   * 获取房源列表
   * @param params 查询参数
   */
  async getHouseList(params?: {
    page?: number
    per_page?: number
    city?: string
    district?: string
    min_price?: number
    max_price?: number
    bedrooms?: number
    status?: string
  }): Promise<HouseListItem[]> {
    const response = await api.get<HouseListItem[]>('/houses', { params })
    return response.data
  }

  /**
   * 获取房源详情
   * @param id 房源ID
   */
  async getHouseDetail(id: number): Promise<HouseDetail> {
    const response = await api.get<HouseDetail>(`/houses/${id}`)
    return response.data
  }

  /**
   * 创建房源 (房东)
   * @param data 房源信息
   */
  async createHouse(data: CreateHouseRequest): Promise<CreateHouseResponse> {
    const response = await api.post<CreateHouseResponse>('/houses', data)
    return response.data
  }

  /**
   * 更新房源 (房东)
   * @param id 房源ID
   * @param data 房源信息
   */
  async updateHouse(id: number, data: CreateHouseRequest): Promise<UpdateHouseResponse> {
    const response = await api.put<UpdateHouseResponse>(`/houses/${id}`, data)
    return response.data
  }

  /**
   * 上传房源图片 (房东)
   * @param id 房源ID
   * @param files 图片文件列表
   */
  async uploadHouseImages(id: number, files: File[]): Promise<UploadImagesResponse> {
    const formData = new FormData()

    files.forEach((file, index) => {
      formData.append(`image_${index}`, file)
    })

    const response = await api.post<UploadImagesResponse>(`/houses/${id}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return response.data
  }

  /**
   * 设置主图 (房东)
   * @param houseId 房源ID
   * @param imageId 图片ID
   */
  async setMainImage(houseId: number, imageId: number): Promise<MessageResponse> {
    const response = await api.put<MessageResponse>(`/houses/${houseId}/images/${imageId}/main`)
    return response.data
  }

  /**
   * 删除房源图片 (房东)
   * @param houseId 房源ID
   * @param imageId 图片ID
   */
  async deleteHouseImage(houseId: number, imageId: number): Promise<MessageResponse> {
    const response = await api.delete<MessageResponse>(`/houses/${houseId}/images/${imageId}`)
    return response.data
  }

  /**
   * 删除房源照片 (按索引)
   * @param houseId 房源ID
   * @param photoIndex 图片索引
   */
  async deletePhoto(houseId: number, photoIndex: number): Promise<MessageResponse> {
    const response = await api.delete<MessageResponse>(`/houses/${houseId}/photos/${photoIndex}`)
    return response.data
  }

  /**
   * 获取我的房源列表
   */
  async getMyHouses(): Promise<HouseListItem[]> {
    const response = await api.get<HouseListItem[]>('/houses/my')
    return response.data
  }

  /**
   * 更新房源状态
   * @param houseId 房源ID
   * @param status 新状态
   */
  async updateHouseStatus(houseId: number, status: string): Promise<MessageResponse> {
    const response = await api.patch<MessageResponse>(`/houses/${houseId}/status`, { status })
    return response.data
  }

  /**
   * 验证房源 (管理员)
   * @param houseId 房源ID
   * @param verified 是否验证通过
   */
  async verifyHouse(houseId: number, verified: boolean): Promise<MessageResponse> {
    const response = await api.patch<MessageResponse>(`/houses/${houseId}/verify`, { verified })
    return response.data
  }

  /**
   * 删除房源
   * @param houseId 房源ID
   */
  async deleteHouse(houseId: number): Promise<MessageResponse> {
    const response = await api.delete<MessageResponse>(`/houses/${houseId}`)
    return response.data
  }
}

export default new HouseService()
